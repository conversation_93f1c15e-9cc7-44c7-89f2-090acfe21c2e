{"name": "Claude Code Enhanced Environment", "image": "mcr.microsoft.com/devcontainers/python:3.12", "features": {"ghcr.io/devcontainers/features/node:1": {"version": "22"}, "ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}, "ghcr.io/devcontainers/features/docker-in-docker:1": {}, "ghcr.io/devcontainers/features/common-utils:2": {"installZsh": true, "configureZshAsDefaultShell": true, "installOhMyZsh": true}}, "onCreateCommand": "sudo apt update && sudo apt install -y neovim tmux", "postCreateCommand": "npm install -g @anthropic-ai/claude-code && curl -LsSf https://astral.sh/uv/install.sh | sh && pip install --upgrade pip && pip install poetry black isort mypy flake8 pytest pytest-cov bandit safety jupyter ipython rich click typer requests httpx aiohttp pandas numpy matplotlib seaborn python-dotenv pydantic fastapi uvicorn && if [ -f /workspaces/streamvideorec/~/.tmux.conf ]; then cp /workspaces/streamvideorec/~/.tmux.conf ~/.tmux.conf; fi", "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.pylint", "ms-python.black-formatter", "ms-python.isort", "ms-python.mypy-type-checker", "ms-python.flake8", "ms-toolsai.jupyter", "github.vscode-pull-request-github", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-azuretools.vscode-docker", "christian-kohler.path-intellisense", "ms-vscode.makefile-tools"], "settings": {"python.defaultInterpreterPath": "/usr/local/bin/python", "python.linting.enabled": true, "python.linting.pylintEnabled": true, "[python]": {"editor.defaultFormatter": "ms-python.black-formatter", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}}, "isort.args": ["--profile", "black"], "terminal.integrated.defaultProfile.linux": "zsh"}}}, "remoteUser": "vscode", "forwardPorts": [8000, 8080, 3000, 5000], "portsAttributes": {"8000": {"label": "FastAPI/Django"}, "8080": {"label": "Alternative Web"}, "3000": {"label": "Node.js/React"}, "5000": {"label": "Flask"}}}