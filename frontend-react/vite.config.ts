import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
// @ts-ignore
import postcssVariant from 'postcss-font-variant'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  css: {
    postcss: {
      plugins: [
        postcssVariant()
      ]
    }
  },
  server: {
    port: 8080,
    proxy: {
      // Proxy API requests to the backend during development
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      }
    }
  }
})
