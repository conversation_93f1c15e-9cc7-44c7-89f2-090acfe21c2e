import axios from 'axios';
import { Post, Comment } from '../types';

// Define API response structures
interface PaginationInfo {
  total: number;
}

interface PostsApiResponse {
  data: Post[];
  pagination: PaginationInfo;
}

interface CommentsApiResponse {
  data: Comment[];
}

// API URLs - use relative URLs for proxying in development
const API_URL = '/api/v1/posts/technology';
const COMMENTS_API_URL = '/api/v1/comments';
const AUTH_API_URL = '/api/v1/auth';

// Create a clean axios instance to avoid interceptor duplication issues
export const apiClient = axios.create({
  withCredentials: true, // Enable sending cookies with requests
});

// Store CSRF token
let csrfToken: string | null = null;

// Function to set CSRF token
export const setCsrfToken = (token: string) => {
  if (!token) return;
  console.debug('Setting CSRF token');
  csrfToken = token;
};

// Function to get CSRF token
export const getCsrfToken = () => csrfToken;

// Helper to extract CSRF token from any response
export const extractCsrfToken = (response: { data?: { csrf_token?: string } }): void => {
  if (response?.data?.csrf_token) {
    setCsrfToken(response.data.csrf_token);
  }
};

// Function to load CSRF token from cookie if server sets it in future
export const loadCsrfFromCookie = (): string | null => {
  // Read CSRF token from cookie if exists (for future implementation)
  const cookies = document.cookie.split(';');
  let tokenFromCookie = null;
  
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'csrf_token') {
      tokenFromCookie = decodeURIComponent(value);
      break;
    }
  }
  
  if (tokenFromCookie) {
    setCsrfToken(tokenFromCookie);
  }
  
  return tokenFromCookie;
};

// Add CSRF token to non-GET requests
apiClient.interceptors.request.use(
  (config) => {
    // Only add CSRF token to mutation requests (non-GET)
    if (config.method !== 'get' && csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken;
    }
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor to handle authentication errors
apiClient.interceptors.response.use(
  (response) => {
    // Extract and store any CSRF token in response
    extractCsrfToken(response);
    return response;
  },
  async (error) => {
    // Handle 401 Unauthorized errors
    if (error.response && error.response.status === 401) {
      console.warn('Authentication error detected');
      
      // Prevent infinite loops by checking URL
      const isRefreshRequest = error.config.url.includes('/refresh');
      
      // Only attempt refresh if this isn't already a refresh request
      if (!isRefreshRequest && error.config && !error.config.__isRetryRequest) {
        try {
          // Attempt to refresh the token
          const response = await apiClient.post(`${AUTH_API_URL}/refresh`);
          
          // If successful, update CSRF token
          extractCsrfToken(response);
          
          // Retry the original request
          error.config.__isRetryRequest = true;
          return apiClient(error.config);
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          // Dispatch logout event
          window.dispatchEvent(new CustomEvent('auth:logout'));
        }
      } else {
        // Either this is a failed refresh request or already retried
        window.dispatchEvent(new CustomEvent('auth:logout'));
      }
    }
    
    return Promise.reject(error);
  }
);

// Auth API calls
export const loginWithGoogle = async (token: string) => {
  const response = await apiClient.post(`${AUTH_API_URL}/google`, { token });
  
  // Store CSRF token
  extractCsrfToken(response);
  
  return response.data;
};

export const logout = async () => {
  try {
    // Make sure CSRF token is included in request
    console.debug(`Logout with CSRF token: ${csrfToken ? 'present' : 'missing'}`);
    
    const response = await apiClient.post(`${AUTH_API_URL}/logout`);
    
    // Clear CSRF token
    setCsrfToken('');
    return response.data;
  } catch (error) {
    console.error('Logout error:', error);
    // Clear CSRF token even on error
    setCsrfToken('');
    throw error;
  }
};

export const refreshToken = async () => {
  try {
    const response = await apiClient.post(`${AUTH_API_URL}/refresh`);
    
    // Update CSRF token
    extractCsrfToken(response);
    
    return true;
  } catch (error) {
    console.error('Token refresh failed:', error);
    return false;
  }
};

export const getCurrentUser = async () => {
  const response = await apiClient.get(`${AUTH_API_URL}/user`);
  return response.data;
};

export const fetchPosts = async (
  limit = 20, 
  page = 1,
  min_upvotes?: number,
  min_comments?: number,
  insightful?: boolean,
  newsworthy?: boolean
): Promise<PostsApiResponse> => {
  // The backend requires a date parameter; if we want all dates we need to modify the query
  const params: {
    limit: number;
    page: number;
    all_dates: boolean;
    min_upvotes?: number;
    min_comments?: number;
    insightful?: boolean;
    newsworthy?: boolean;
  } = { 
    limit, 
    page,
    all_dates: true, // This will be used to modify the query in our custom middleware
    min_upvotes, // Only include if defined
    min_comments, // Only include if defined
    insightful,
    newsworthy
  };
  
  const response = await apiClient.get<PostsApiResponse>(API_URL, { params });
  return response.data;
};

export const fetchComments = async (postId: string): Promise<CommentsApiResponse> => {
  const response = await apiClient.get<CommentsApiResponse>(`${COMMENTS_API_URL}/${postId}`);
  return response.data;
}; 