import React, { useEffect, useState } from 'react';
import SearchBar from '../components/ui/SearchBar';
import FilterControls from '../components/ui/FilterControls';
import PostsList from '../components/posts/PostsList';
import ScrollToTop from '../components/ui/ScrollToTop';
import { usePosts } from '../context/PostsContext';
import './HomePage.css';

const HomePage: React.FC = () => {
  const { loading, error, filteredPosts, posts, expandedPostId } = usePosts();
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  
  useEffect(() => {
    console.log('HomePage rendered, posts count:', posts.length);
    console.log('Filtered posts count:', filteredPosts.length);
    console.log('Loading state:', loading);
    console.log('Error state:', error);
  }, [posts, filteredPosts, loading, error]);
  
  const toggleFilters = () => {
    setIsFilterExpanded(!isFilterExpanded);
  };
  
  if (loading && posts.length === 0) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading posts...</p>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="error-container">
        <h2>Error loading posts</h2>
        <p>{error}</p>
      </div>
    );
  }

  // When a post is expanded, we're in "post view mode"
  // Only render the expanded post
  if (expandedPostId) {
    return (
      <div className="home-page single-post-view">
        <div className="main-content">
          <div className="dashboard-card posts-card">
            <div className="card-content">
              <PostsList singlePostMode={true} />
            </div>
          </div>
        </div>
        <ScrollToTop />
      </div>
    );
  }

  // Normal view mode with all posts and filters
  return (
    <div className="home-page">
      <div className="main-content">
        <div className="dashboard-card search-and-filters">
          <div className="card-header collapsible-header" onClick={toggleFilters}>
            <h2>Search & Filters</h2>
            <button className="collapse-button">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                {isFilterExpanded ? 
                  <polyline points="18 15 12 9 6 15"></polyline> : 
                  <polyline points="6 9 12 15 18 9"></polyline>
                }
              </svg>
            </button>
          </div>
          <div className={`card-content ${isFilterExpanded ? 'expanded' : 'collapsed'}`}>
            <SearchBar />
            <FilterControls />
          </div>
        </div>
        
        <div className="dashboard-card posts-card">
          <div className="card-content">
            <PostsList />
          </div>
        </div>
      </div>
      <ScrollToTop />
    </div>
  );
};

export default HomePage; 