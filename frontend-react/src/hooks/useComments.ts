import { useState, useEffect, useRef } from 'react';
import { Comment } from '../types';
import { fetchComments } from '../services/api';

interface CommentCache {
  [postId: string]: Comment[];
}

const useComments = (postId: string | null) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const commentCacheRef = useRef<CommentCache>({});

  useEffect(() => {
    if (!postId) {
      setComments([]);
      return;
    }

    // Check if we already have these comments in cache
    if (commentCacheRef.current[postId]) {
      setComments(commentCacheRef.current[postId]);
      return;
    }

    const loadComments = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetchComments(postId);
        const fetchedComments = response.data;
        
        // Cache the comments
        commentCacheRef.current[postId] = fetchedComments;
        
        setComments(fetchedComments);
        setLoading(false);
      } catch (err) {
        setError('Failed to load comments');
        setLoading(false);
        console.error('Error fetching comments:', err);
      }
    };

    loadComments();
  }, [postId]);

  return { comments, loading, error };
};

export default useComments; 