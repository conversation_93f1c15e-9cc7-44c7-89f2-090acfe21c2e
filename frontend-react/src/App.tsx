import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { ThemeProvider } from './context/ThemeContext';
import { PostsProvider } from './context/PostsContext';
import { AuthProvider } from './context/AuthContext';
import HomePage from './pages/HomePage';
import Login from './components/Login';
import ProtectedRoute from './components/ProtectedRoute';
import Footer from './components/layout/Footer';
import Header from './components/layout/Header';
import './App.css';
import { useTheme } from './context/ThemeContext';
import { usePosts } from './context/PostsContext';

// Component to handle URL params and browser history
const URLHandler = () => {
  const { togglePostExpansion, expandedPostId } = usePosts();
  const [processed, setProcessed] = React.useState(false);
  
  // Check for post ID in URL on initial load only
  useEffect(() => {
    // Only run once and only if we haven't already processed a URL
    if (!processed && !expandedPostId) {
      try {
        const url = new URL(window.location.href);
        const postId = url.searchParams.get('post');
        
        if (postId) {
          console.log('Found postId in URL, expanding post:', postId);
          // Set a small timeout to ensure PostsContext is fully initialized
          setTimeout(() => {
            togglePostExpansion(postId);
            setProcessed(true);
          }, 200);
        } else {
          // Mark as processed even if no postId found
          setProcessed(true);
        }
      } catch (error) {
        console.error('Error processing URL parameters:', error);
        setProcessed(true);
      }
    }
  }, [togglePostExpansion, expandedPostId, processed]);
  
  return null;
};

function App() {
  const ThemeUpdater = () => {
    const { theme } = useTheme();
    React.useEffect(() => {
      document.body.className = theme === 'dark' ? 'dark-mode' : 'light-mode';
    }, [theme]);
    return null;
  };

  // Get client ID from environment variable
  const googleClientId = import.meta.env.VITE_GOOGLE_CLIENT_ID || '';

  return (
    <GoogleOAuthProvider 
      clientId={googleClientId}
      onScriptLoadError={() => console.error("Google API script failed to load")}
      onScriptLoadSuccess={() => console.log("Google API script loaded successfully")}
    >
      <ThemeProvider>
        <ThemeUpdater />
        <Router>
          <AuthProvider>
            <PostsProvider>
              <URLHandler />
              <div style={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
                <Header />
                <main style={{ flex: '1' }}>
                  <Routes>
                    <Route path="/login" element={<Login />} />
                    <Route path="/" element={
                      <ProtectedRoute>
                        <HomePage />
                      </ProtectedRoute>
                    } />
                    <Route path="*" element={<Navigate to="/" replace />} />
                  </Routes>
                </main>
                <Footer />
              </div>
            </PostsProvider>
          </AuthProvider>
        </Router>
      </ThemeProvider>
    </GoogleOAuthProvider>
  );
}

export default App;
