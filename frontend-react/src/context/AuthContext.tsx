import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
import { CredentialResponse } from '@react-oauth/google';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import { 
  loginWithGoogle, 
  logout as apiLogout,
  refreshToken as apiRefreshToken,
  getCurrentUser,
  loadCsrfFromCookie
} from '../services/api';

interface AuthState {
  isAuthenticated: boolean;
  user: {
    email: string;
    name: string;
    picture: string;
    google_id: string;
    created_at: string;
  } | null;
  loading: boolean;
  error: string | null;
}

interface AuthContextType extends AuthState {
  login: (response: CredentialResponse) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Create a wrapper component to access navigate inside Context Provider
const AuthContextContent: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const [state, setState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    loading: true,
    error: null
  });

  // Try to load CSRF token on init
  useEffect(() => {
    loadCsrfFromCookie();
  }, []);

  // Define logout function using useCallback to prevent recreation on every render
  const logout = useCallback(async (): Promise<void> => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      if (state.isAuthenticated) {
        console.log('Logging out user...');
        await apiLogout();
        toast.success('Logged out successfully');
      }
    } catch (err) {
      console.error('Logout error:', err instanceof Error ? err.message : String(err));
      toast.warning('Session ended locally, but server returned an error');
    } finally {
      // Always update state regardless of API success/failure
      setState({
        isAuthenticated: false,
        user: null,
        loading: false,
        error: null
      });
      
      console.log('User logged out, navigating to login page');
      // Navigate to login page only if we're not already there
      if (location.pathname !== '/login') {
        navigate('/login', { replace: true });
      }
    }
  }, [state.isAuthenticated, navigate, location.pathname]);

  // Listen for global logout events (e.g., from API interceptors)
  useEffect(() => {
    const handleGlobalLogout = () => {
      console.warn('Global logout event received');
      logout();
    };
    
    window.addEventListener('auth:logout', handleGlobalLogout);
    
    return () => {
      window.removeEventListener('auth:logout', handleGlobalLogout);
    };
  }, [logout]);

  // Initial authentication check
  useEffect(() => {
    const initAuth = async () => {
      try {
        // Try to get current user information
        const userInfo = await getCurrentUser();
        
        console.log('User info retrieved successfully');
        setState({
          isAuthenticated: true,
          user: userInfo,
          loading: false,
          error: null
        });
      } catch (err) {
        console.error('Authentication error:', err instanceof Error ? err.message : String(err));
        setState({
          isAuthenticated: false,
          user: null,
          loading: false,
          error: null
        });
        
        // If not on login page, redirect
        if (location.pathname !== '/login') {
          navigate('/login', { replace: true });
        }
      }
    };
    
    initAuth();
  }, [navigate, location.pathname]);

  // Add token refresh logic
  useEffect(() => {
    // Refresh every 50 minutes (assuming 1-hour session)
    const refreshInterval = setInterval(() => {
      if (state.isAuthenticated && !state.loading) {
        refreshToken();
      }
    }, 50 * 60 * 1000);

    return () => clearInterval(refreshInterval);
  }, [state.isAuthenticated, state.loading]);

  const refreshToken = useCallback(async (): Promise<void> => {
    try {
      if (!state.isAuthenticated) return;
      
      console.log('Refreshing auth token...');
      const success = await apiRefreshToken();
      
      if (!success) {
        console.warn('Token refresh failed, logging out');
        await logout();
      } else {
        console.log('Token refreshed successfully');
      }
    } catch (err) {
      console.error('Token refresh error:', err instanceof Error ? err.message : String(err));
      await logout();
    }
  }, [state.isAuthenticated, logout]);

  const login = useCallback(async (response: CredentialResponse): Promise<void> => {
    try {
      console.log('Logging in with Google credentials');
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      if (!response.credential) {
        throw new Error('No credential received from Google');
      }
      
      // Use the API service to login
      const data = await loginWithGoogle(response.credential);
      
      // Update authentication state
      setState({
        isAuthenticated: true,
        user: data.user,
        loading: false,
        error: null
      });
      
      toast.success('Login successful');
      
      // Navigate to home page
      navigate('/');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Authentication failed';
      console.error('Login error:', errorMessage);
      toast.error(`Login failed: ${errorMessage}`);
      
      setState({
        isAuthenticated: false,
        user: null,
        loading: false,
        error: errorMessage
      });
    }
  }, [navigate]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Use listener for session events
  useEffect(() => {
    const visibilityChanged = async () => {
      if (document.visibilityState === 'visible' && state.isAuthenticated) {
        // When tab becomes visible, check if session is still valid
        try {
          await getCurrentUser();
          // If successful, session is still valid
          console.log('Session still valid on visibility change');
        } catch {
          // Ignore the specific error - we just need to know it failed
          console.log('Session check failed on visibility change, refreshing token');
          await refreshToken();
        }
      }
    };
    
    document.addEventListener('visibilitychange', visibilityChanged);
    return () => {
      document.removeEventListener('visibilitychange', visibilityChanged);
    };
  }, [state.isAuthenticated, refreshToken]);

  const value = {
    ...state,
    login,
    logout,
    refreshToken,
    clearError
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <AuthContextContent>
      {children}
    </AuthContextContent>
  );
};

export default AuthContext; 