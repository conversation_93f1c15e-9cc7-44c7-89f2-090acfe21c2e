import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback, useRef } from 'react';
import { Post } from '../types';
import { fetchPosts } from '../services/api';

interface FiltersType {
  searchTerm: string;
  minUpvotes: number;
  minComments: number;
  insightful: boolean;
  newsworthy: boolean;
}

type PostsContextValue = {
  posts: Post[];
  filteredPosts: Post[];
  totalPosts: number;
  loading: boolean;
  error: string | null;
  filters: FiltersType;
  expandedPostId: string | null;
  updateFilters: (newFilters: Partial<FiltersType>) => void;
  resetFilters: () => void;
  togglePostExpansion: (postId: string) => void;
  loadMorePosts: () => void;
  hasMore: boolean;
  markPostAsRead: (postId: string) => void;
  isPostRead: (postId: string) => boolean;
}

const PostsContext = createContext<PostsContextValue | undefined>(undefined);

interface PostsProviderProps {
  children: ReactNode;
}

// LocalStorage key for read posts
const READ_POSTS_KEY = 'smartersubs_read_posts';

// Helper to get read posts from localStorage
const getReadPostsFromStorage = (): string[] => {
  const storedReadPosts = localStorage.getItem(READ_POSTS_KEY);
  return storedReadPosts ? JSON.parse(storedReadPosts) : [];
};

export const PostsProvider: React.FC<PostsProviderProps> = ({ children }: PostsProviderProps) => {
  // Use a ref to track if the initial fetch has been performed
  const initialFetchPerformed = useRef(false);
  
  const [posts, setPosts] = useState<Post[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<Post[]>([]);
  const [totalPosts, setTotalPosts] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedPostId, setExpandedPostId] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [readPostIds, setReadPostIds] = useState<string[]>(getReadPostsFromStorage());
  
  const [filters, setFilters] = useState<FiltersType>({
    searchTerm: '',
    minUpvotes: 0,
    minComments: 0,
    insightful: true,
    newsworthy: true
  });

  // Fetch posts from API
  const fetchPostsPage = useCallback(async (pageNum: number) => {
    // Prevent duplicate fetches
    if (pageNum === 1 && initialFetchPerformed.current) {
      console.log('Initial fetch already performed, skipping duplicate request');
      return;
    }
    
    try {
      console.log('Fetching posts page:', pageNum);
      setLoading(true);
      
      const response = await fetchPosts(
        20, 
        pageNum,
        filters.minUpvotes > 0 ? filters.minUpvotes : undefined,
        filters.minComments > 0 ? filters.minComments : undefined,
        filters.insightful ? true : undefined,
        filters.newsworthy ? true : undefined
      );
      
      console.log('Posts fetched successfully:', response.data.length);
      
      if (pageNum === 1) {
        setPosts(response.data);
        initialFetchPerformed.current = true;
      } else {
        setPosts(prevPosts => [...prevPosts, ...response.data]);
      }
      
      setTotalPosts(response.pagination.total);
      setHasMore(response.data.length === 20); // If fewer than 20 posts returned, we're at the end
      setLoading(false);
    } catch (err) {
      console.error('Error fetching posts:', err);
      setError('Failed to load posts');
      setLoading(false);
    }
  }, [filters.minUpvotes, filters.minComments, filters.insightful, filters.newsworthy]);

  // Initial load - only fetch once on component mount
  useEffect(() => {
    if (!initialFetchPerformed.current) {
      console.log('Performing initial posts fetch');
      fetchPostsPage(1);
    }
  }, [fetchPostsPage]);
  
  // Load more posts
  const loadMorePosts = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchPostsPage(nextPage);
    }
  };

  // Apply filters whenever dependencies change
  useEffect(() => {
    if (posts.length === 0) return;

    let result = [...posts];

    // Apply search filter (this is done on the client side)
    if (filters.searchTerm) {
      const searchTermLower = filters.searchTerm.toLowerCase();
      result = result.filter(post => 
        post.title.toLowerCase().includes(searchTermLower) || 
        (post.text && post.text.toLowerCase().includes(searchTermLower))
      );
    }

    // Mark read posts
    result = result.map(post => ({
      ...post,
      isRead: readPostIds.includes(post.id)
    }));

    // Always ensure posts are sorted by date in descending order
    result.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    setFilteredPosts(result);
  }, [posts, filters.searchTerm, readPostIds]);

  // Update filters - we reset and reload from the backend when filters change
  const updateFilters = useCallback((newFilters: Partial<FiltersType>) => {
    console.log('Updating filters:', newFilters);
    setFilters((prev: FiltersType) => ({ ...prev, ...newFilters }));
    
    // Reset pagination when filters change
    setPage(1);
    setPosts([]);
    
    // Reset the initial fetch flag so we fetch again with new filters
    initialFetchPerformed.current = false;
    fetchPostsPage(1);
  }, [fetchPostsPage]);

  // Reset filters
  const resetFilters = useCallback(() => {
    console.log('Resetting all filters');
    setFilters({
      searchTerm: '',
      minUpvotes: 0,
      minComments: 0,
      insightful: true,
      newsworthy: true
    });
    
    // Reset pagination when filters change
    setPage(1);
    setPosts([]);
    
    // Reset the initial fetch flag so we fetch again with default filters
    initialFetchPerformed.current = false;
    fetchPostsPage(1);
  }, [fetchPostsPage]);

  // Listen for popstate (browser back/forward) events
  useEffect(() => {
    const handlePopState = (event: PopStateEvent) => {
      // If post ID exists in state, use it
      const postId = event.state?.postId;
      
      // If no post ID in state, close expanded post
      if (!postId) {
        setExpandedPostId(null);
      } else {
        setExpandedPostId(postId);
      }
    };

    window.addEventListener('popstate', handlePopState);
    
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  // Toggle post expansion
  const togglePostExpansion = (postId: string) => {
    // Get current expanded post
    const currentExpandedId = expandedPostId;
    
    // Update expanded post ID state
    if (currentExpandedId === postId) {
      setExpandedPostId(null);
    } else {
      setExpandedPostId(postId);
    }
    
    // Handle browser history
    if (currentExpandedId === postId) {
      // Closing post - go back if possible, otherwise replace state
      if (window.history.state?.postId) {
        window.history.back();
      } else {
        window.history.replaceState(null, '', window.location.pathname);
      }
    } else if (currentExpandedId) {
      // Switching from one post to another
      window.history.replaceState({ postId }, '', `?post=${postId}`);
    } else {
      // Opening a post from closed state
      window.history.pushState({ postId }, '', `?post=${postId}`);
    }
    
    // Mark as read when expanded
    if (postId && !readPostIds.includes(postId)) {
      markPostAsRead(postId);
    }
  };

  // Mark post as read
  const markPostAsRead = (postId: string) => {
    if (!readPostIds.includes(postId)) {
      const updatedReadPosts = [...readPostIds, postId];
      setReadPostIds(updatedReadPosts);
      
      // Update localStorage
      localStorage.setItem(READ_POSTS_KEY, JSON.stringify(updatedReadPosts));
    }
  };

  // Check if post is read
  const isPostRead = (postId: string): boolean => {
    return readPostIds.includes(postId);
  };

  return (
    <PostsContext.Provider
      value={{
        posts,
        filteredPosts,
        totalPosts,
        loading,
        error,
        filters,
        expandedPostId,
        updateFilters,
        resetFilters,
        togglePostExpansion,
        loadMorePosts,
        hasMore,
        markPostAsRead,
        isPostRead
      }}
    >
      {children}
    </PostsContext.Provider>
  );
};

export const usePosts = (): PostsContextValue => {
  const context = useContext(PostsContext);
  if (context === undefined) {
    throw new Error('usePosts must be used within a PostsProvider');
  }
  return context;
}; 