/**
 * Checks if a URL is an image based on its extension
 * @param url The URL to check
 * @returns boolean indicating if the URL is an image
 */
export const isImageUrl = (url: string): boolean => {
  if (!url) return false;
  
  // Try to extract file extension
  const extension = url
    .split('.')
    .pop()
    ?.toLowerCase()
    .split('?')[0]; // Remove query params
  
  if (!extension) return false;
  
  // Check against common image extensions
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
  return imageExtensions.includes(extension);
}; 