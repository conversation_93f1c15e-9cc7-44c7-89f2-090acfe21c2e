import React from 'react';

export interface Post {
  id: string;
  title: string;
  text: string | null;
  url: string;
  date: string;
  upvotes: number;
  number_of_comments: number;
  permalink: string;
  subreddit_name: string;
  flair: string;
  isRead?: boolean;
  newsworthiness_score?: number;
  insightfulness_score?: number;
  reason?: string;
}

export interface Comment {
  id: string;
  author: string;
  text: string;
  date: string;
  score: number;
  post_id: string;
  community: string;
}

export interface ThemeContextState {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
} 