import React from 'react';
import { format } from 'date-fns';
import useComments from '../../hooks/useComments';
import MarkdownRenderer from '../ui/MarkdownRenderer';
import './CommentsList.css';

interface CommentsListProps {
  postId: string;
}

const CommentsList: React.FC<CommentsListProps> = ({ postId }: CommentsListProps) => {
  const { comments, loading, error } = useComments(postId);

  if (loading) {
    return <div className="loading">Loading comments...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  if (comments.length === 0) {
    return <div className="no-comments">No comments available</div>;
  }

  return (
    <div className="comments-section">
      <h3 className="comments-title">Comments ({comments.length})</h3>
      <div className="comments-list">
        {comments.map(comment => (
          <div className="comment" key={comment.id}>
            <div className="comment-header">
              <span className="comment-author">{comment.author}</span>
              <span className="comment-score">Upvotes: {comment.score}</span>
              {comment.date && (
                <span className="comment-date">
                  {format(new Date(comment.date), 'MMM d, yyyy h:mm a')}
                </span>
              )}
            </div>
            <div className="comment-text">
              <MarkdownRenderer content={comment.text} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CommentsList; 