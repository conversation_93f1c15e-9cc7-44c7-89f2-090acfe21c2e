import React from 'react';
import ThemeToggle from '../ui/ThemeToggle';
import { useAuth } from '../../context/AuthContext';
import { Link } from 'react-router-dom';
import { usePosts } from '../../context/PostsContext';
import './Header.css';

const Header: React.FC = () => {
  const { logout, user } = useAuth();
  const { expandedPostId, togglePostExpansion } = usePosts();
  
  const handleLogout = async () => {
    await logout();
  };

  const handleHomeClick = (e: React.MouseEvent) => {
    // If we're already on the home page, prevent default navigation
    if (window.location.pathname === '/') {
      e.preventDefault();
    }
    
    // Scroll to top
    window.scrollTo(0, 0);
    
    // Collapse any expanded post
    if (expandedPostId) {
      togglePostExpansion(expandedPostId);
    }
  };
  
  return (
    <header className="app-header">
      <div className="header-container">
        <div className="logo">
          <Link to="/" onClick={handleHomeClick} className="logo-link">
            <div className="logo-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                <line x1="9" y1="9" x2="9.01" y2="9"></line>
                <line x1="15" y1="9" x2="15.01" y2="9"></line>
              </svg>
            </div>
            <div className="logo-text">
              <h1>SmarterSubs</h1>
            </div>
          </Link>
        </div>
        <div className="header-controls">
          <ThemeToggle />
          {user && (
            <div className="user-info">
              <div className="user-avatar">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <span className="user-email">{user.email}</span>
              <button className="logout-button" onClick={handleLogout}>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                  <polyline points="16 17 21 12 16 7"></polyline>
                  <line x1="21" y1="12" x2="9" y2="12"></line>
                </svg>
                <span>Logout</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header; 