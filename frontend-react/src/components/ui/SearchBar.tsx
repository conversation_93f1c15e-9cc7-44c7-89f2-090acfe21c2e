import React, { useState, ChangeEvent, KeyboardEvent } from 'react';
import { usePosts } from '../../context/PostsContext';
import './SearchBar.css';

const SearchBar: React.FC = () => {
  const { filters, updateFilters, resetFilters } = usePosts();
  const [inputValue, setInputValue] = useState(filters.searchTerm);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleReset = () => {
    setInputValue('');
    resetFilters();
  };

  // Function to trigger search
  const performSearch = () => {
    updateFilters({ searchTerm: inputValue });
  };

  // Handle Enter key press on input
  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      performSearch();
    }
  };

  return (
    <div className="search-section">
      <div className="search-input-container">
        <div className="search-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
        </div>
        <input 
          type="text" 
          id="search-input"
          placeholder="Search in titles..." 
          value={inputValue} 
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          aria-label="Search posts"
        />
      </div>
      
      <div className="search-buttons">
        <button 
          className="btn btn-primary"
          onClick={performSearch}
        >
          Search
        </button>
        <button 
          className="btn btn-secondary"
          onClick={handleReset}
        >
          Reset
        </button>
      </div>
    </div>
  );
};

export default SearchBar; 