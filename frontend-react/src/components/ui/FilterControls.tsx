import React, { ChangeEvent, useState, KeyboardEvent } from 'react';
import { usePosts } from '../../context/PostsContext';
import './FilterControls.css';

const FilterControls: React.FC = () => {
  const { filters, updateFilters, expandedPostId, togglePostExpansion } = usePosts();
  const [upvotesInput, setUpvotesInput] = useState<string>(filters.minUpvotes.toString());
  const [commentsInput, setCommentsInput] = useState<string>(filters.minComments.toString());

  const handleMinUpvotesChange = (e: ChangeEvent<HTMLInputElement>) => {
    setUpvotesInput(e.target.value);
  };

  const handleMinCommentsChange = (e: ChangeEvent<HTMLInputElement>) => {
    setCommentsInput(e.target.value);
  };

  const handleUpvotesKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const value = parseInt(upvotesInput) || 0;
      updateFilters({ minUpvotes: value });
    }
  };

  const handleCommentsKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const value = parseInt(commentsInput) || 0;
      updateFilters({ minComments: value });
    }
  };

  const handleInsightfulChange = (e: ChangeEvent<HTMLInputElement>) => {
    // Scroll to top of the page
    window.scrollTo(0, 0);
    
    // Collapse any expanded post
    if (expandedPostId) {
      togglePostExpansion(expandedPostId);
    }
    
    // Update the filter
    updateFilters({ insightful: e.target.checked });
  };

  const handleNewsworthyChange = (e: ChangeEvent<HTMLInputElement>) => {
    // Scroll to top of the page
    window.scrollTo(0, 0);
    
    // Collapse any expanded post
    if (expandedPostId) {
      togglePostExpansion(expandedPostId);
    }
    
    // Update the filter
    updateFilters({ newsworthy: e.target.checked });
  };

  return (
    <div className="filters-row">
      <div className="filters-column">
        <div className="filter-label">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M12 19V5M5 12l7-7 7 7"/>
          </svg>
          <span>Min Upvotes:</span>
        </div>
        <input
          className="filter-input"
          id="min-upvotes"
          type="number"
          min="0"
          value={upvotesInput}
          onChange={handleMinUpvotesChange}
          onKeyDown={handleUpvotesKeyDown}
        />
      </div>
      
      <div className="filters-column">
        <div className="filter-label">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
          </svg>
          <span>Min Comments:</span>
        </div>
        <input
          className="filter-input"
          id="min-comments"
          type="number"
          min="0"
          value={commentsInput}
          onChange={handleMinCommentsChange}
          onKeyDown={handleCommentsKeyDown}
        />
      </div>

      <div className="filters-column">
        <div className="filter-label">
          <span>Filter by:</span>
        </div>
        <div className="filter-toggles">
          <label htmlFor="insightful" className={`filter-toggle ${filters.insightful ? 'active' : ''}`}>
            <input
              id="insightful"
              type="checkbox"
              checked={filters.insightful}
              onChange={handleInsightfulChange}
            />
            <span className="toggle-icon">💡</span>
            <span className="toggle-text">Insightful</span>
          </label>
          
          <label htmlFor="newsworthy" className={`filter-toggle ${filters.newsworthy ? 'active' : ''}`}>
            <input
              id="newsworthy"
              type="checkbox"
              checked={filters.newsworthy}
              onChange={handleNewsworthyChange}
            />
            <span className="toggle-icon">📰</span>
            <span className="toggle-text">Newsworthy</span>
          </label>
        </div>
      </div>
    </div>
  );
};

export default FilterControls; 