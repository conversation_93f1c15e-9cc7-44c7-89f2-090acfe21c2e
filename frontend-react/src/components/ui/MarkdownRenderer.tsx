import React, { useEffect, useState } from 'react';
import { marked } from 'marked';
import DOMPurify from 'dompurify';

interface MarkdownRendererProps {
  content: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content }) => {
  const [htmlContent, setHtmlContent] = useState<string>('');

  useEffect(() => {
    const processContent = async () => {
      if (!content) return;
      
      // Convert image URLs to markdown image syntax with HTTPS enforcement
      const imageUrlRegex = /(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)(?:\?[^\s]*)?)/gi;
      const processedContent = content.replace(imageUrlRegex, (match) => {
        // Force HTTPS
        const secureUrl = match.replace(/^http:/i, 'https:');
        return `![](${secureUrl})`;
      });
      
      const rawHtml = await marked.parse(processedContent);
      const sanitizedHtml = DOMPurify.sanitize(rawHtml, {
        ALLOWED_TAGS: [
          'a', 'b', 'blockquote', 'br', 'code', 'em', 'h1', 'h2', 'h3', 'h4', 
          'h5', 'h6', 'hr', 'i', 'li', 'ol', 'p', 'pre', 'strong', 'ul', 'img'
        ],
        ALLOWED_ATTR: ['href', 'target', 'src', 'alt', 'title', 'class', 'rel'],
        FORBID_ATTR: [
          'onerror', 'onclick', 'onload', 'onmouseover', 'onmouseout',
          'onmousedown', 'onmouseup', 'onkeydown', 'onkeypress', 'onkeyup',
          'onchange', 'onfocus', 'onblur', 'onreset', 'onselect', 'onsubmit',
          'ontouchstart', 'ontouchmove', 'ontouchend', 'ontouchcancel'
        ],
        ADD_URI_SAFE_ATTR: ['src'],
        ALLOWED_URI_REGEXP: /^https:\/\//i // Only allow HTTPS URLs
      });
      
      // Fix for SS-02: Add rel="noopener noreferrer nofollow" to all links with target="_blank"
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = sanitizedHtml;
      
      const links = tempDiv.querySelectorAll('a[target="_blank"]');
      links.forEach((link) => {
        link.setAttribute('rel', 'noopener noreferrer nofollow');
      });
      
      // Fix for SS-03: Remove all SVG images to prevent XSS through SVG scripting
      const images = tempDiv.querySelectorAll('img');
      images.forEach((img) => {
        const src = img.getAttribute('src');
        if (src && src.toLowerCase().endsWith('.svg')) {
          img.parentNode?.removeChild(img);
        }
      });
      
      setHtmlContent(tempDiv.innerHTML);
    };

    processContent();
  }, [content]);

  if (!content) return null;
  
  return <div className="markdown-body" dangerouslySetInnerHTML={{ __html: htmlContent }} />;
};

export default MarkdownRenderer; 