import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();
  
  useEffect(() => {
    console.log('ProtectedRoute state:', { isAuthenticated, loading, path: location.pathname });
  }, [isAuthenticated, loading, location.pathname]);

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Verifying authentication...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    console.log('Not authenticated, redirecting to login');
    // Only redirect to login if we're not already there and we've finished loading
    // Use {replace: true} to avoid adding to history stack
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  console.log('Authentication verified, rendering protected content');
  return <>{children}</>;
};

export default ProtectedRoute; 