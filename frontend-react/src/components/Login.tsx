import React, { useEffect } from 'react';
import { GoogleLogin } from '@react-oauth/google';
import { useAuth } from '../context/AuthContext';
import './Login.css';

const Login: React.FC = () => {
  const { login, error, clearError } = useAuth();

  useEffect(() => {
    // Clear any error on component mount
    clearError();
    
    // Only run this effect once on mount
  }, [clearError]);

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="logo">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
            <line x1="9" y1="9" x2="9.01" y2="9"></line>
            <line x1="15" y1="9" x2="15.01" y2="9"></line>
          </svg>
        </div>
        <h1>Welcome to SmarterSubs</h1>
        <p>Please sign in to continue</p>
        
        {error && <div className="error-message">{error}</div>}
        
        <div className="google-login-button">
          <GoogleLogin
            onSuccess={(response) => {
              console.log('Login success, processing credentials', response);
              clearError(); // Clear any previous errors
              login(response);
            }}
            onError={() => {
              console.error('Login Failed');
            }}
            useOneTap={false} 
            type="standard"
            theme="filled_blue"
            shape="rectangular"
            width="280"
            logo_alignment="center"
            context="signin"
            text="signin_with"
            ux_mode="popup"
          />
        </div>
        
        <div className="notes">
          Your data is secure. We use Google Sign-In to provide a seamless authentication experience.
        </div>
      </div>
    </div>
  );
};

export default Login; 