import React, { useRef, useCallback } from 'react';
import { usePosts } from '../../context/PostsContext';
import PostItem from './PostItem';
import './PostsList.css';

interface PostsListProps {
  singlePostMode?: boolean;
}

const PostsList: React.FC<PostsListProps> = ({ singlePostMode = false }) => {
  const { filteredPosts, loading, error, totalPosts, loadMorePosts, hasMore, expandedPostId, posts } = usePosts();
  const observer = useRef<IntersectionObserver | null>(null);
  
  // Reference to the last post element for infinite scrolling
  const lastPostElementRef = useCallback((node: HTMLDivElement | null) => {
    if (loading || singlePostMode) return; // Don't use intersection observer in single post mode
    
    // Disconnect previous observer
    if (observer.current) observer.current.disconnect();
    
    // Create new observer
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        loadMorePosts();
      }
    });
    
    // Observe the last element
    if (node) observer.current.observe(node);
  }, [loading, hasMore, loadMorePosts, singlePostMode]);
  
  if (error) {
    return <div className="posts-error">{error}</div>;
  }

  // In single post mode, only show the expanded post
  if (singlePostMode && expandedPostId) {
    // First check in filtered posts
    let expandedPost = filteredPosts.find(post => post.id === expandedPostId);
    
    // If not found in filtered posts (due to filtering), check in all posts
    if (!expandedPost) {
      expandedPost = posts.find(post => post.id === expandedPostId);
    }
    
    if (!expandedPost) {
      return (
        <div className="posts-list-container single-post-mode">
          <div className="no-posts-message">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            <h3>Post Not Found</h3>
            <p>The post you're looking for could not be found.</p>
          </div>
        </div>
      );
    }
    
    return (
      <div className="posts-list-container single-post-mode">
        <PostItem post={expandedPost} singlePostMode={true} />
      </div>
    );
  }
  
  return (
    <div className="posts-list-container">
      <div className="results-info">
        <p id="total-results">
          {filteredPosts.length === 0 && !loading
            ? "No posts match your criteria" 
            : `Showing ${filteredPosts.length} of ${totalPosts} posts`}
        </p>
      </div>
      
      <div className="posts-container" id="posts-container">
        {filteredPosts.length > 0 ? (
          filteredPosts.map((post, index) => {
            if (filteredPosts.length === index + 1) {
              // Add ref to last element
              return (
                <div key={post.id} ref={lastPostElementRef}>
                  <PostItem post={post} />
                </div>
              );
            } else {
              return <PostItem key={post.id} post={post} />;
            }
          })
        ) : !loading ? (
          <div className="no-posts-message">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            <h3>No Posts Found</h3>
            <p>Try adjusting your search criteria or filters to see more results.</p>
          </div>
        ) : null}
      </div>
      
      {loading && !singlePostMode && (
        <div className="posts-loading-indicator">
          <div className="loading-spinner"></div>
          <p>Loading more posts...</p>
        </div>
      )}
    </div>
  );
};

export default PostsList;
