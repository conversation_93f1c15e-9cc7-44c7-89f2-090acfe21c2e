import React, { useRef, useEffect } from 'react';
import { format } from 'date-fns';
import { Post } from '../../types';
import { usePosts } from '../../context/PostsContext';
import MarkdownRenderer from '../ui/MarkdownRenderer';
import { isImageUrl } from '../../utils/isImageUrl';
import './PostItem.css';
import CommentsList from '../comments/CommentsList';

interface PostItemProps {
  post: Post;
  singlePostMode?: boolean;
}

const PostItem: React.FC<PostItemProps> = ({ post, singlePostMode = false }) => {
  const { expandedPostId, togglePostExpansion, isPostRead } = usePosts();
  const isExpanded = expandedPostId === post.id || singlePostMode;
  const postRef = useRef<HTMLElement>(null);
  const isRead = post.isRead || isPostRead(post.id);

  useEffect(() => {
    if (isExpanded && postRef.current) {
      postRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [isExpanded]);

  const formattedDate = post.date 
    ? format(new Date(post.date), 'MMM d, yyyy')
    : 'Date unknown';

  const handlePostClick = () => {
    if (!singlePostMode) {
      togglePostExpansion(post.id);
    }
  };

  const handleBackClick = () => {
    togglePostExpansion(post.id);
  };

  // Truncate title if it has more than 8 words and is not expanded
  const truncateTitle = (title: string): string => {
    if (isExpanded) return title;
    
    const words = title.split(' ');
    if (words.length > 15) {
      return words.slice(0, 15).join(' ') + '...';
    }
    return title;
  };

  return (
    <article ref={postRef} className={`post-card ${isExpanded ? 'expanded' : ''} ${isRead ? 'read' : ''} ${singlePostMode ? 'single-post-mode' : ''}`}>
      {singlePostMode && (
        <div className="back-button-container top">
          <button onClick={handleBackClick} className="back-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="19" y1="12" x2="5" y2="12"></line>
              <polyline points="12 19 5 12 12 5"></polyline>
            </svg>
            Back to posts
          </button>
        </div>
      )}
      
      <div className={`post-title-clickable-area ${singlePostMode ? 'not-clickable' : ''}`} onClick={!singlePostMode ? handlePostClick : undefined}>
        {!singlePostMode && <h2 className="post-title">{truncateTitle(post.title)}</h2>}
        
        <div className="post-info-container">
          <div className="post-info">
            <span className="post-date">{formattedDate}</span>
            {post.subreddit_name && (
              <span className="post-subreddit">r/{post.subreddit_name}</span>
            )}
          </div>
          
          <div className="post-meta">
            <span className="post-upvotes" title="Upvotes">{post.upvotes}</span>
            <span className="post-comments" title="Comments">{post.number_of_comments}</span>
            
            {post.insightfulness_score !== undefined && post.insightfulness_score > 0 && (
              <span className="post-insightful" title="Insightfulness Score">
                {post.insightfulness_score}
              </span>
            )}
            
            {post.newsworthiness_score !== undefined && post.newsworthiness_score > 0 && (
              <span className="post-newsworthy" title="Newsworthiness Score">
                {post.newsworthiness_score}
              </span>
            )}
            
            {((post.insightfulness_score !== undefined && post.insightfulness_score > 0) || 
              (post.newsworthiness_score !== undefined && post.newsworthiness_score > 0)) && 
              post.reason && (
              <div className="post-info-icon" title={post.reason}>ℹ️</div>
            )}
          </div>
        </div>
      </div>
      
      {isExpanded && (
        <div className="expanded-only">
          <h2 className="expanded-title">{post.title}</h2>
          {post.text && (
            <div className="post-content">
              <div className="post-text">
                <MarkdownRenderer content={post.text} />
              </div>
            </div>
          )}
          
          {post.url && (
            isImageUrl(post.url) ? (
              <div className="post-content">
                <div className="post-image">
                  <img src={post.url} alt={post.title} loading="lazy" />
                </div>
              </div>
            ) : (
              <div className="post-content">
                <div className="post-link">
                  <a href={post.url} target="_blank" rel="noopener noreferrer">
                    {post.url}
                  </a>
                </div>
              </div>
            )
          )}
          
          <div className="post-actions">
            <a 
              href={`https://reddit.com${post.permalink}`} 
              target="_blank" 
              rel="noopener noreferrer"
              className="reddit-link"
            >
              View on Reddit
            </a>
          </div>
          
          <CommentsList postId={post.id} />
          
          {singlePostMode && (
            <div className="back-button-container bottom">
              <button onClick={handleBackClick} className="back-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="19" y1="12" x2="5" y2="12"></line>
                  <polyline points="12 19 5 12 12 5"></polyline>
                </svg>
                Back to posts
              </button>
            </div>
          )}
        </div>
      )}
    </article>
  );
};

export default PostItem; 