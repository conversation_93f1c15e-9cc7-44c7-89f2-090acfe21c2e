{"name": "frontend-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start:dev": "vite --port 8080"}, "dependencies": {"@fontsource-variable/inter": "^5.2.5", "@fontsource-variable/roboto-slab": "^5.2.5", "@react-oauth/google": "^0.12.1", "axios": "^1.8.4", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "jwt-decode": "^4.0.0", "marked": "^15.0.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^6.22.1", "react-toastify": "^9.1.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "@typescript-eslint/typescript-estree": "^8.29.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss-font-variant": "^5.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.3.5"}}