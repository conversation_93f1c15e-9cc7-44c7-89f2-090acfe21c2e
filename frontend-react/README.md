# SmarterSubs Frontend

## Typography System

The application uses a modern, responsive typography system with variable fonts for better performance and aesthetics.

### Key Features

- **Variable Fonts**: Using `InterVariable` and `RobotoSlabVariable` for optimized performance and fluid weight ranges
- **Fluid Type Scale**: Responsive font sizes using CSS `clamp()` to scale appropriately across device sizes
- **Consistent Line Heights**: Standardized line heights for readability
- **Semantic Font Weights**: Using CSS variables for font weights
- **Markdown Styling**: Custom styling for markdown content with proper typographic hierarchy

### CSS Variables

The typography system uses the following CSS variables:

#### Font Weights
- `--fw-regular`: 400
- `--fw-medium`: 500
- `--fw-semibold`: 600
- `--fw-bold`: 700

#### Type Scale
- `--step--2`: Smallest text (captions, metadata)
- `--step--1`: Small text (footnotes, secondary text)
- `--step-0`: Base font size (body text)
- `--step-1`: Medium headings
- `--step-2`: Large headings
- `--step-3`: Extra large headings
- `--step-4`: Display headings

#### Line Heights
- `--lh-heading`: 1.25 (for headings)
- `--lh-body`: 1.6 (for body text)
- `--lh-code`: 1.5 (for code blocks)

### Typography Files

- `/src/styles/typography.css`: Main typography definitions and variables
- `/src/styles/markdown.css`: Specific styling for markdown content
- `/src/assets/fonts/`: Contains variable font files
