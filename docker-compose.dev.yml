version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: smartsubs-postgres
    ports:
      - "5433:5432"
    environment:
      POSTGRES_DB: smartsubs
      POSTGRES_USER: smartsubsuser
      POSTGRES_PASSWORD: Yeahyeah22!
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U smartsubsuser -d smartsubs"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:alpine
    container_name: smartsubs-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

volumes:
  postgres_data:
  redis_data: