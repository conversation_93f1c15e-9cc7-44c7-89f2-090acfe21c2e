#!/bin/bash

echo "Starting servers with separate log files..."

# Create logs directory
mkdir -p logs

# Function to cleanup background processes on exit
cleanup() {
    echo "Shutting down services..."
    jobs -p | xargs -r kill
    exit 0
}

trap cleanup SIGINT SIGTERM

# Start services with separate log files
echo "Starting Backend server (logs: logs/backend.log)..."
cd backend
FLASK_ENV=development FLASK_APP=api.py python -m flask run > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
cd ..

echo "Starting React Frontend (logs: logs/frontend.log)..."
cd frontend-react
npm run start:dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..

echo "Starting background services (logs: logs/background.log)..."
cd backend
./run_smartsubs_hourly.sh > ../logs/background.log 2>&1 &
SERVICE_PID=$!
cd ..

echo ""
echo "✅ All services started with logging"
echo ""
echo "To monitor logs in separate terminals:"
echo "  tail -f logs/backend.log"
echo "  tail -f logs/frontend.log" 
echo "  tail -f logs/background.log"
echo ""
echo "Or watch all logs:"
echo "  tail -f logs/*.log"
echo ""
echo "Services running:"
echo "  Backend API: http://127.0.0.1:5000"
echo "  Frontend: http://localhost:8080"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for all background processes
wait