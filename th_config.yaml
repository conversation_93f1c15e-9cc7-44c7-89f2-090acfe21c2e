# This is a default configuration file for th_parallel.py

# Settings with defaults - modify as needed
# ---------------------------------------------------
path: .                       # Target directory path (defaults to current directory)
output_file: output.txt       # Output filename (defaults to output.txt)

# Optional settings
# ---------------------------------------------------
# Token budget control
#token_budget: 5001          # Maximum tokens allowed (min 5000)

# Ignore settings
respect_git_ignore: true      # Respect .gitignore rules (true/false)
use_thignore: true            # Use .thignore file for ignore patterns (true/false)

# Note: File exclusion patterns are now defined in .thignore files
# Create a .thignore file in your project directory with patterns similar to .gitignore
# Example .thignore file contents:
#
# # Output files
# output.txt
# output_report.html
# th_config.yaml
#
# # Common files and directories to exclude
# *.log
# *.db
# .git/
# __pycache__/
#
# # File type exclusions
# *.exe
# *.dll
# *.so
# *.obj
# *.png
# *.jpg
# *.pdf

# Parallel processing settings
parallel:
  enabled: true                # Enable parallel processing
  max_workers: null            # Maximum number of workers (null = auto)
  rate_limit: 0.0              # Minimum seconds between API calls

# Git filtering
git_only: true                # Only process Git-tracked files

# Quiet mode (suppress non-error output)
quiet: false

# Binary handling
# If set to 'yes', files detected as binary will be excluded
# from further processing and replaced by a short placeholder.
exclude-binary-files: yes

# Code-file filtering
code-files-only: yes          # yes → only process files recognised as source-code

# Files to always include in full (no token budget restrictions)
# ---------------------------------------------------
include-in-full:
  # Add paths to important files that should always be included in full
  # Examples:
- backend/requirements.txt
- aiutils/prompt1.txt
  # - another/critical/file.md