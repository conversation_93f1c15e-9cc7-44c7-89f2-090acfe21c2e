#!/bin/bash
# Shell script to set up database environment variables

# Default values
DEFAULT_HOST="localhost"
DEFAULT_PORT="5432"
DEFAULT_DB="smartsubs"
DEFAULT_USER="smartsubsuser"

# Text formatting
CYAN='\033[0;36m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${CYAN}Database Environment Setup${NC}"
echo -e "${CYAN}=========================${NC}"
echo "Enter the PostgreSQL connection details (or press Enter to use defaults):"

# Get input with defaults
read -p "Database Host (default: $DEFAULT_HOST): " PG_HOST
PG_HOST=${PG_HOST:-$DEFAULT_HOST}

read -p "Database Port (default: $DEFAULT_PORT): " PG_PORT
PG_PORT=${PG_PORT:-$DEFAULT_PORT}

read -p "Database Name (default: $DEFAULT_DB): " PG_DB
PG_DB=${PG_DB:-$DEFAULT_DB}

read -p "Database User (default: $DEFAULT_USER): " PG_USER
PG_USER=${PG_USER:-$DEFAULT_USER}

# For password, use -s flag to hide input and don't provide a default
read -s -p "Database Password (required): " PG_PASSWORD
echo ""

if [ -z "$PG_PASSWORD" ]; then
    echo -e "${RED}Error: Password cannot be empty${NC}"
    exit 1
fi

# Prompt for Reddit API credentials
echo -e "\n${CYAN}Reddit API Setup${NC}"
echo -e "${CYAN}================${NC}"
echo "Enter your Reddit API credentials (required for accessing Reddit):"

read -p "Reddit Client ID: " REDDIT_CLIENT_ID
if [ -z "$REDDIT_CLIENT_ID" ]; then
    echo -e "${RED}Error: Reddit Client ID cannot be empty${NC}"
    exit 1
fi

read -s -p "Reddit Client Secret: " REDDIT_CLIENT_SECRET
echo ""
if [ -z "$REDDIT_CLIENT_SECRET" ]; then
    echo -e "${RED}Error: Reddit Client Secret cannot be empty${NC}"
    exit 1
fi

# Prompt for Google OAuth credentials
echo -e "\n${CYAN}Google OAuth Setup${NC}"
echo -e "${CYAN}=================${NC}"
echo "Enter your Google OAuth credentials (required for user authentication):"

read -p "Google Client ID: " GOOGLE_CLIENT_ID
if [ -z "$GOOGLE_CLIENT_ID" ]; then
    echo -e "${RED}Error: Google Client ID cannot be empty${NC}"
    exit 1
fi

read -s -p "Google Client Secret: " GOOGLE_CLIENT_SECRET
echo ""
if [ -z "$GOOGLE_CLIENT_SECRET" ]; then
    echo -e "${RED}Error: Google Client Secret cannot be empty${NC}"
    exit 1
fi

read -p "Google Redirect URI (default: http://localhost:8080): " GOOGLE_REDIRECT_URI
if [ -z "$GOOGLE_REDIRECT_URI" ]; then
    GOOGLE_REDIRECT_URI="http://localhost:8080"
    echo -e "${YELLOW}Using default Google Redirect URI: $GOOGLE_REDIRECT_URI${NC}"
fi

# Generate a strong JWT secret key
JWT_SECRET_KEY=$(openssl rand -base64 32)
echo -e "${GREEN}Generated a secure JWT secret key${NC}"

# Prompt for API URL for frontend
read -p "API URL for frontend (default: http://127.0.0.1:5000): " API_URL
if [ -z "$API_URL" ]; then
    API_URL="http://127.0.0.1:5000"
    echo -e "${YELLOW}Using default API URL: $API_URL${NC}"
fi

# Prompt for AI model configurations
echo -e "\n${CYAN}AI Model Setup${NC}"
echo -e "${CYAN}==============${NC}"
echo "You can configure up to 5 AI models. Enter details for each model."
echo "Models are used in priority order, with Model #1 being tried first."
echo "To stop adding models, leave the model name empty when prompted."

# Initialize arrays to store model configurations
MODEL_NAMES=()
API_KEYS=()
MODEL_COUNT=0
MAX_MODELS=5

for i in $(seq 1 $MAX_MODELS); do
    echo -e "\n${YELLOW}Model #$i Configuration:${NC}"
    
    read -p "Model Name (e.g., 'openai/gpt-4', 'anthropic/claude-3', etc.): " MODEL_NAME
    if [ -z "$MODEL_NAME" ]; then
        # User wants to stop adding models
        if [ $MODEL_COUNT -eq 0 ]; then
            echo -e "${RED}At least one model configuration is required.${NC}"
            i=$((i-1)) # Repeat this iteration
            continue
        else
            # User has added at least one model and wants to stop
            break
        fi
    fi
    
    # For API key, hide input
    read -s -p "API Key for $MODEL_NAME (required): " API_KEY
    echo ""
    
    if [ -z "$API_KEY" ]; then
        echo -e "${RED}Error: API Key cannot be empty${NC}"
        i=$((i-1)) # Repeat this iteration
        continue
    fi
    
    # Add to arrays
    MODEL_NAMES+=("$MODEL_NAME")
    API_KEYS+=("$API_KEY")
    MODEL_COUNT=$((MODEL_COUNT+1))
    
    echo -e "${GREEN}Model #$i configuration saved.${NC}"
    
    # If we've reached max models, exit the loop
    if [ $MODEL_COUNT -eq $MAX_MODELS ]; then
        echo -e "${YELLOW}Maximum number of models ($MAX_MODELS) reached.${NC}"
        break
    fi
done

echo -e "\n${GREEN}Configured $MODEL_COUNT model(s).${NC}"

# Determine paths for .env files
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
ROOT_ENV_PATH="$SCRIPT_DIR/.env"
BACKEND_DIR="$SCRIPT_DIR/backend"
BACKEND_ENV_PATH="$BACKEND_DIR/.env"
AIUTILS_DIR="$SCRIPT_DIR/aiutils"
AIUTILS_ENV_PATH="$AIUTILS_DIR/.env"
FRONTEND_DIR="$SCRIPT_DIR/frontend-react"
FRONTEND_ENV_PATH="$FRONTEND_DIR/.env"

# Create the environment content for root and backend
ENV_CONTENT="# Database configuration
PG_HOST=$PG_HOST
PG_PORT=$PG_PORT
PG_DB=$PG_DB
PG_USER=$PG_USER
PG_PASSWORD=$PG_PASSWORD

# Reddit API configuration
REDDIT_CLIENT_ID=$REDDIT_CLIENT_ID
REDDIT_CLIENT_SECRET=$REDDIT_CLIENT_SECRET

# Google OAuth configuration
GOOGLE_CLIENT_ID=$GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET=$GOOGLE_CLIENT_SECRET
GOOGLE_REDIRECT_URI=$GOOGLE_REDIRECT_URI
JWT_SECRET_KEY=$JWT_SECRET_KEY
"

# Create AI utils .env content
AIUTILS_ENV_CONTENT="# AI model configuration"
for i in $(seq 0 $((MODEL_COUNT-1))); do
    INDEX=$((i+1))
    AIUTILS_ENV_CONTENT="$AIUTILS_ENV_CONTENT
MODEL_NAME_$INDEX=${MODEL_NAMES[$i]}
API_KEY_$INDEX=${API_KEYS[$i]}"
done

# Create frontend .env content
FRONTEND_ENV_CONTENT="VITE_GOOGLE_CLIENT_ID=$GOOGLE_CLIENT_ID
VITE_API_URL=$API_URL"

# Write files
# Write root .env
echo "$ENV_CONTENT" > "$ROOT_ENV_PATH"
echo -e "${GREEN}Created root .env file at $ROOT_ENV_PATH${NC}"

# Write backend .env (ensure directory exists)
mkdir -p "$BACKEND_DIR"
echo "$ENV_CONTENT" > "$BACKEND_ENV_PATH"
echo -e "${GREEN}Created backend .env file at $BACKEND_ENV_PATH${NC}"

# Write aiutils .env (ensure directory exists)
mkdir -p "$AIUTILS_DIR"
echo "$AIUTILS_ENV_CONTENT" > "$AIUTILS_ENV_PATH"
echo -e "${GREEN}Created AI utilities .env file at $AIUTILS_ENV_PATH${NC}"

# Write frontend .env (if directory exists)
if [ -d "$FRONTEND_DIR" ]; then
    echo "$FRONTEND_ENV_CONTENT" > "$FRONTEND_ENV_PATH"
    echo -e "${GREEN}Created frontend .env file at $FRONTEND_ENV_PATH${NC}"
else
    echo -e "${YELLOW}Frontend directory not found. Skipping frontend .env creation.${NC}"
fi

echo -e "${GREEN}Environment setup complete!${NC}"
echo -e "${YELLOW}IMPORTANT: The .env files contain sensitive information. Do not commit them to version control.${NC}" 