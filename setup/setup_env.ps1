#!/usr/bin/env pwsh
# PowerShell script to set up database environment variables

# Default values
$default_host = "localhost"
$default_port = "5432"
$default_db = "smartsubs"
$default_user = "smartsubsuser"

# Prompt for database connection details with defaults
Write-Host "Database Environment Setup" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan
Write-Host "Enter the PostgreSQL connection details (or press Enter to use defaults):"

$pg_host = Read-Host "Database Host (default: $default_host)"
if ([string]::IsNullOrWhiteSpace($pg_host)) { $pg_host = $default_host }

$pg_port = Read-Host "Database Port (default: $default_port)"
if ([string]::IsNullOrWhiteSpace($pg_port)) { $pg_port = $default_port }

$pg_db = Read-Host "Database Name (default: $default_db)"
if ([string]::IsNullOrWhiteSpace($pg_db)) { $pg_db = $default_db }

$pg_user = Read-Host "Database User (default: $default_user)"
if ([string]::IsNullOrWhiteSpace($pg_user)) { $pg_user = $default_user }

# For password, don't provide a default and use SecureString for input
$pg_password = Read-Host -AsSecureString "Database Password (required)"
$pg_password_text = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
    [Runtime.InteropServices.Marshal]::SecureStringToBSTR($pg_password)
)

if ([string]::IsNullOrWhiteSpace($pg_password_text)) {
    Write-Host "Error: Password cannot be empty" -ForegroundColor Red
    exit 1
}

# Prompt for Reddit API credentials
Write-Host "`nReddit API Setup" -ForegroundColor Cyan
Write-Host "================" -ForegroundColor Cyan
Write-Host "Enter your Reddit API credentials (required for accessing Reddit):"

$reddit_client_id = Read-Host "Reddit Client ID"
if ([string]::IsNullOrWhiteSpace($reddit_client_id)) {
    Write-Host "Error: Reddit Client ID cannot be empty" -ForegroundColor Red
    exit 1
}

$reddit_client_secret = Read-Host -AsSecureString "Reddit Client Secret"
$reddit_client_secret_text = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
    [Runtime.InteropServices.Marshal]::SecureStringToBSTR($reddit_client_secret)
)

if ([string]::IsNullOrWhiteSpace($reddit_client_secret_text)) {
    Write-Host "Error: Reddit Client Secret cannot be empty" -ForegroundColor Red
    exit 1
}

# Prompt for Google OAuth credentials
Write-Host "`nGoogle OAuth Setup" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan
Write-Host "Enter your Google OAuth credentials (required for user authentication):"

$google_client_id = Read-Host "Google Client ID"
if ([string]::IsNullOrWhiteSpace($google_client_id)) {
    Write-Host "Error: Google Client ID cannot be empty" -ForegroundColor Red
    exit 1
}

# For Google Client Secret, use SecureString for input
$google_client_secret_secure = Read-Host -AsSecureString "Google Client Secret"
$google_client_secret_text = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
    [Runtime.InteropServices.Marshal]::SecureStringToBSTR($google_client_secret_secure)
)

if ([string]::IsNullOrWhiteSpace($google_client_secret_text)) {
    Write-Host "Error: Google Client Secret cannot be empty" -ForegroundColor Red
    exit 1
}

# Prompt for Google Redirect URI with default value
$google_redirect_uri = Read-Host "Google Redirect URI (default: http://localhost:8080)"
if ([string]::IsNullOrWhiteSpace($google_redirect_uri)) {
    $google_redirect_uri = "http://localhost:8080"
    Write-Host "Using default Google Redirect URI: $google_redirect_uri" -ForegroundColor Yellow
}

# Generate a strong JWT secret key
$jwt_secret_key = -join ((65..90) + (97..122) + (48..57) | Get-Random -Count 32 | ForEach-Object {[char]$_})
Write-Host "Generated a secure JWT secret key" -ForegroundColor Green

# Prompt for API URL with default value
$api_url = Read-Host "API URL for frontend (default: http://127.0.0.1:5000)"
if ([string]::IsNullOrWhiteSpace($api_url)) {
    $api_url = "http://127.0.0.1:5000"
    Write-Host "Using default API URL: $api_url" -ForegroundColor Yellow
}

# Prompt for AI model configurations
Write-Host "`nAI Model Setup" -ForegroundColor Cyan
Write-Host "==============" -ForegroundColor Cyan
Write-Host "You can configure up to 5 AI models. Enter details for each model."
Write-Host "Models are used in priority order, with Model #1 being tried first."
Write-Host "To stop adding models, leave the model name empty when prompted."

# Initialize arrays to store model configurations
$model_names = @()
$api_keys = @()
$model_count = 0
$max_models = 5

for ($i = 1; $i -le $max_models; $i++) {
    Write-Host "`nModel #$i Configuration:" -ForegroundColor Yellow
    
    $model_name = Read-Host "Model Name (e.g., 'openai/gpt-4', 'anthropic/claude-3', etc.)"
    if ([string]::IsNullOrWhiteSpace($model_name)) {
        # User wants to stop adding models
        if ($model_count -eq 0) {
            Write-Host "At least one model configuration is required." -ForegroundColor Red
            $i-- # Decrement to repeat this iteration
            continue
        } else {
            # User has added at least one model and wants to stop
            break
        }
    }
    
    # For API key, use SecureString for input
    $api_key_secure = Read-Host -AsSecureString "API Key for $model_name (required)"
    $api_key = [Runtime.InteropServices.Marshal]::PtrToStringAuto(
        [Runtime.InteropServices.Marshal]::SecureStringToBSTR($api_key_secure)
    )
    
    if ([string]::IsNullOrWhiteSpace($api_key)) {
        Write-Host "Error: API Key cannot be empty" -ForegroundColor Red
        $i-- # Decrement to repeat this iteration
        continue
    }
    
    # Add to arrays
    $model_names += $model_name
    $api_keys += $api_key
    $model_count++
    
    Write-Host "Model #$i configuration saved." -ForegroundColor Green
    
    # If we've reached max models, exit the loop
    if ($model_count -eq $max_models) {
        Write-Host "Maximum number of models ($max_models) reached." -ForegroundColor Yellow
        break
    }
}

Write-Host "`nConfigured $model_count model(s)." -ForegroundColor Green

# Determine paths for .env files
$script_path = Split-Path -Parent $MyInvocation.MyCommand.Path
$root_env_path = Join-Path -Path $script_path -ChildPath ".env"
$backend_path = Join-Path -Path $script_path -ChildPath "backend"
$backend_env_path = Join-Path -Path $backend_path -ChildPath ".env"
$aiutils_path = Join-Path -Path $script_path -ChildPath "aiutils"
$aiutils_env_path = Join-Path -Path $aiutils_path -ChildPath ".env"
$frontend_path = Join-Path -Path $script_path -ChildPath "frontend-react"
$frontend_env_path = Join-Path -Path $frontend_path -ChildPath ".env"

# Create .env content for database, Reddit API, and Google OAuth
$env_content = @"
# Database configuration
PG_HOST=$pg_host
PG_PORT=$pg_port
PG_DB=$pg_db
PG_USER=$pg_user
PG_PASSWORD=$pg_password_text

# Reddit API configuration
REDDIT_CLIENT_ID=$reddit_client_id
REDDIT_CLIENT_SECRET=$reddit_client_secret_text

# Google OAuth configuration
GOOGLE_CLIENT_ID=$google_client_id
GOOGLE_CLIENT_SECRET=$google_client_secret_text
GOOGLE_REDIRECT_URI=$google_redirect_uri
JWT_SECRET_KEY=$jwt_secret_key
"@

# Create AI utils .env content
$aiutils_env_content = "# AI model configuration`n"
for ($i = 0; $i -lt $model_count; $i++) {
    $index = $i + 1
    $aiutils_env_content += "MODEL_NAME_$index=$($model_names[$i])`n"
    $aiutils_env_content += "API_KEY_$index=$($api_keys[$i])`n"
}

# Create frontend .env content
$frontend_env_content = @"
VITE_GOOGLE_CLIENT_ID=$google_client_id
VITE_API_URL=$api_url
"@

# Write files
try {
    # Write root .env
    Set-Content -Path $root_env_path -Value $env_content -Force
    Write-Host "Created root .env file at $root_env_path" -ForegroundColor Green
    
    # Write backend .env (ensure directory exists)
    if (-not (Test-Path -Path $backend_path)) {
        New-Item -Path $backend_path -ItemType Directory -Force | Out-Null
    }
    Set-Content -Path $backend_env_path -Value $env_content -Force
    Write-Host "Created backend .env file at $backend_env_path" -ForegroundColor Green
    
    # Write aiutils .env (ensure directory exists)
    if (-not (Test-Path -Path $aiutils_path)) {
        New-Item -Path $aiutils_path -ItemType Directory -Force | Out-Null
    }
    Set-Content -Path $aiutils_env_path -Value $aiutils_env_content -Force
    Write-Host "Created AI utilities .env file at $aiutils_env_path" -ForegroundColor Green
    
    # Write frontend .env (if directory exists)
    if (Test-Path -Path $frontend_path) {
        Set-Content -Path $frontend_env_path -Value $frontend_env_content -Force
        Write-Host "Created frontend .env file at $frontend_env_path" -ForegroundColor Green
    }
    else {
        Write-Host "Frontend directory not found. Skipping frontend .env creation." -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Error writing .env files: $_" -ForegroundColor Red
    exit 1
}

Write-Host "Environment setup complete!" -ForegroundColor Green
Write-Host "IMPORTANT: The .env files contain sensitive information. Do not commit them to version control." -ForegroundColor Yellow 