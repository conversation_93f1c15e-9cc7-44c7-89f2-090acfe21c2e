#!/usr/bin/env python
import subprocess
import argparse
import os
import sys
import datetime
import time
import getpass
import configparser
import platform

# Configuration - Update these values as needed
PG_BIN_PATH = r"C:\Program Files\PostgreSQL\16\bin"
PG_USER = "smartsubsuser"  # Regular user
PG_ADMIN_USER = "postgres"  # Superuser for admin operations
PG_DB = "smartsubs"
PG_RESTORED_DB = "smartsubs_restored"
BACKUP_DIR = r"C:\code\sql_dump"
BACKUP_FILE = os.path.join(BACKUP_DIR, "smartsubs_backup.sql")

# Ensure backup directory exists
os.makedirs(BACKUP_DIR, exist_ok=True)
os.makedirs(os.path.join(BACKUP_DIR, "permissions"), exist_ok=True)

# Global password storage to avoid repeated prompts
passwords = {}


def get_password(username):
    """Get password for a user, prompting only once"""
    if username not in passwords:
        passwords[username] = getpass.getpass(
            f"Enter password for PostgreSQL user {username}: ")
    return passwords[username]


def configure_pgpass():
    """Configure a temporary pgpass file for the session"""
    global passwords

    # Only create pgpass if we have passwords
    if not passwords:
        return None

    # Create appropriate pgpass file based on platform
    if platform.system() == "Windows":
        pgpass_path = os.path.join(os.environ.get(
            "APPDATA", ""), "postgresql", "pgpass.conf")
        os.makedirs(os.path.dirname(pgpass_path), exist_ok=True)
    else:
        pgpass_path = os.path.join(os.path.expanduser("~"), ".pgpass")

    # Check if file exists and if we should append
    existing_content = ""
    if os.path.exists(pgpass_path):
        with open(pgpass_path, 'r') as f:
            existing_content = f.read()

    # Write or append to pgpass file
    with open(pgpass_path, 'w') as f:
        f.write(existing_content)
        # Add lines for each user
        for user, password in passwords.items():
            if f"*:*:*:{user}:" not in existing_content:
                f.write(f"*:*:*:{user}:{password}\n")

    # Set permissions (required for Linux/Unix)
    if platform.system() != "Windows":
        os.chmod(pgpass_path, 0o600)

    return pgpass_path


def run_command(command, shell=True, env=None):
    """Run a command and return its output"""
    try:
        print(f"Running: {command}")

        # Configure environment with PGPASSWORD if needed
        custom_env = os.environ.copy()
        if env:
            custom_env.update(env)

        result = subprocess.run(
            command,
            shell=shell,
            check=True,
            text=True,
            capture_output=True,
            env=custom_env
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error executing command: {e}")
        print(f"Command output: {e.stdout}")
        print(f"Command error: {e.stderr}")
        return None


def pg_command(command, admin=False):
    """Run a psql command"""
    psql_path = os.path.join(PG_BIN_PATH, "psql")
    user = PG_ADMIN_USER if admin else PG_USER

    # Set up environment with password
    env = {}
    if user in passwords:
        env["PGPASSWORD"] = passwords[user]

    return run_command(f'"{psql_path}" -U {user} -d {PG_DB} -c "{command}"', env=env)


def backup_database():
    """Backup the smartsubs database"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = os.path.join(BACKUP_DIR, f"smartsubs_backup_{timestamp}.sql")

    # Create the backup with more comprehensive options
    pg_dump_path = os.path.join(PG_BIN_PATH, "pg_dump")

    # Modified backup command to avoid database creation/dropping:
    # -U: admin user to ensure we have proper permissions
    # --no-owner: skip restoration of object ownership
    # -v: verbose mode for better logging
    # Removed --create and --clean flags to avoid dropping/creating the database
    command = f'"{pg_dump_path}" -U {PG_ADMIN_USER} --no-owner -v --schema-only -d {PG_DB} -f "{backup_file}.schema"'

    # Set up environment with password
    env = {}
    if PG_ADMIN_USER in passwords:
        env["PGPASSWORD"] = passwords[PG_ADMIN_USER]

    if run_command(command, env=env) is None:
        print("Schema backup failed!")
        sys.exit(1)

    # Now dump just the data
    data_command = f'"{pg_dump_path}" -U {PG_ADMIN_USER} --no-owner -v --data-only -d {PG_DB} -f "{backup_file}.data"'

    if run_command(data_command, env=env) is None:
        print("Data backup failed!")
        sys.exit(1)

    # Combine the schema and data files
    try:
        with open(backup_file, 'wb') as combined:
            with open(f"{backup_file}.schema", 'rb') as schema:
                combined.write(schema.read())
            with open(f"{backup_file}.data", 'rb') as data:
                combined.write(data.read())

        print(f"Backup completed successfully to: {backup_file}")

        # Create a symlink/copy to the latest backup
        latest_backup = os.path.join(BACKUP_DIR, "smartsubs_backup_latest.sql")
        try:
            if os.path.exists(latest_backup):
                os.remove(latest_backup)
            # On Windows, we need to copy rather than symlink
            with open(backup_file, 'rb') as src, open(latest_backup, 'wb') as dst:
                dst.write(src.read())
            print(f"Created latest backup reference: {latest_backup}")

            # Clean up temporary files
            os.remove(f"{backup_file}.schema")
            os.remove(f"{backup_file}.data")

            # Verify backup file content
            verify_backup(latest_backup)
        except Exception as e:
            print(f"Warning: Could not create latest backup reference: {e}")
    except Exception as e:
        print(f"Error combining backup files: {e}")
        sys.exit(1)


def verify_backup(backup_file):
    """Verify backup file content"""
    try:
        # Check file size
        file_size = os.path.getsize(backup_file)
        print(f"Backup file size: {file_size} bytes")

        if file_size < 1000:
            print("WARNING: Backup file seems very small, it might not contain all data!")

        # Read and count CREATE TABLE statements as a sanity check
        with open(backup_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            table_count = content.count("CREATE TABLE")
            print(f"Found {table_count} CREATE TABLE statements in backup")

            if table_count == 0:
                print("WARNING: No CREATE TABLE statements found in backup!")
    except Exception as e:
        print(f"Warning: Could not verify backup content: {e}")


def extract_permissions():
    """Extract permissions from the source database"""
    perm_dir = os.path.join(BACKUP_DIR, "permissions")
    os.makedirs(perm_dir, exist_ok=True)

    # Database level permissions
    db_perms_file = os.path.join(perm_dir, "db_permissions.sql")
    db_perms_cmd = """
    SELECT 'GRANT ' || string_agg(privilege_type, ', ') || ' ON DATABASE smartsubs TO ' || grantee || ';' 
    FROM information_schema.role_usage_grants 
    WHERE object_name = 'smartsubs'
    GROUP BY grantee;
    """
    db_perms = pg_command(db_perms_cmd)
    with open(db_perms_file, 'w') as f:
        f.write(db_perms if db_perms else "-- No database permissions found")

    # Table level permissions - Enhanced to include more information
    table_perms_file = os.path.join(perm_dir, "table_permissions.sql")
    table_perms_cmd = """
    SELECT 
        '-- Table: ' || table_schema || '.' || table_name || E'\\n' ||
        'GRANT ' || string_agg(privilege_type, ', ') || 
        ' ON TABLE ' || table_schema || '.' || table_name || 
        ' TO ' || grantee || ';' 
    FROM information_schema.role_table_grants 
    WHERE table_catalog = 'smartsubs'
    GROUP BY table_schema, table_name, grantee;
    """
    table_perms = pg_command(table_perms_cmd)
    with open(table_perms_file, 'w') as f:
        f.write(table_perms if table_perms else "-- No table permissions found")

    # Schema permissions (added)
    schema_perms_file = os.path.join(perm_dir, "schema_permissions.sql")
    schema_perms_cmd = """
    SELECT 
        '-- Schema: ' || schema_name || E'\\n' ||
        'GRANT ' || string_agg(privilege_type, ', ') || 
        ' ON SCHEMA ' || schema_name || 
        ' TO ' || grantee || ';' 
    FROM information_schema.role_usage_grants 
    WHERE object_type = 'SCHEMA'
    GROUP BY schema_name, grantee;
    """
    schema_perms = pg_command(schema_perms_cmd)
    with open(schema_perms_file, 'w') as f:
        f.write(schema_perms if schema_perms else "-- No schema permissions found")

    # Default privileges
    def_perms_file = os.path.join(perm_dir, "default_privileges.sql")
    def_perms_cmd = """
    SELECT '-- Default privileges for namespace ' || nspname || E'\\n' ||
           'ALTER DEFAULT PRIVILEGES IN SCHEMA ' || nspname ||
           ' GRANT ' || privilege_type || 
           ' ON ' || object_type || 
           ' TO ' || grantee || ';'
    FROM pg_default_acl a
    JOIN pg_namespace n ON n.oid = a.defaclnamespace
    CROSS JOIN LATERAL aclexplode(a.defaclacl) AS x
    JOIN pg_roles r ON r.oid = x.grantee
    WHERE nspname NOT LIKE 'pg_%';
    """
    def_perms = pg_command(def_perms_cmd)
    with open(def_perms_file, 'w') as f:
        f.write(def_perms if def_perms else "-- No default privileges found")

    return {
        'db_perms': db_perms_file,
        'table_perms': table_perms_file,
        'schema_perms': schema_perms_file,
        'def_perms': def_perms_file
    }


def restore_database():
    """Restore the smartsubs database to a new database with all permissions"""
    psql_path = os.path.join(PG_BIN_PATH, "psql")

    # Check if the backup file exists
    latest_backup = os.path.join(BACKUP_DIR, "smartsubs_backup_latest.sql")
    if not os.path.exists(latest_backup):
        print(f"Error: Backup file not found: {latest_backup}")
        print("Please run --backup first")
        sys.exit(1)

    # Extract permissions before creating the new database
    print("Extracting permissions from source database...")
    perm_files = extract_permissions()

    # Environment for postgres admin commands
    admin_env = {}
    if PG_ADMIN_USER in passwords:
        admin_env["PGPASSWORD"] = passwords[PG_ADMIN_USER]

    # User environment
    user_env = {}
    if PG_USER in passwords:
        user_env["PGPASSWORD"] = passwords[PG_USER]

    # Check if the restored database exists and drop it if it does
    check_db_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d postgres -c "SELECT 1 FROM pg_database WHERE datname = \'{PG_RESTORED_DB}\'"'
    db_exists = run_command(check_db_cmd, env=admin_env)

    if "1 row" in db_exists if db_exists else False:
        print(f"Database {PG_RESTORED_DB} exists, dropping it...")

        # First terminate all connections to the database
        terminate_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d postgres -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = \'{PG_RESTORED_DB}\'"'
        run_command(terminate_cmd, env=admin_env)

        # Then drop the database
        drop_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d postgres -c "DROP DATABASE IF EXISTS {PG_RESTORED_DB}"'
        if run_command(drop_cmd, env=admin_env) is None:
            print("Failed to drop the existing database!")
            sys.exit(1)

    # Create the new database - using admin user for this operation
    print(f"Creating new database {PG_RESTORED_DB}...")
    create_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d postgres -c "CREATE DATABASE {PG_RESTORED_DB} WITH TEMPLATE template0 ENCODING=\'UTF8\'"'
    if run_command(create_cmd, env=admin_env) is None:
        print("Failed to create the new database!")
        sys.exit(1)

    # Grant access to the regular user
    grant_access_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE {PG_RESTORED_DB} TO {PG_USER}"'
    if run_command(grant_access_cmd, env=admin_env) is None:
        print("Warning: Could not grant all privileges to the regular user")

    # Wait a moment for database creation to complete
    time.sleep(2)

    # Restore the data - using enhanced psql options and admin user
    print("Restoring database data...")
    restore_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -v ON_ERROR_STOP=1 -f "{latest_backup}"'
    if run_command(restore_cmd, env=admin_env) is None:
        print("Failed to restore the database data!")
        sys.exit(1)

    # Fix ownership issues by reassigning all objects to the regular user
    print("Fixing table ownerships...")

    # Get all schemas in the database
    schema_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -t -c "SELECT nspname FROM pg_namespace WHERE nspname NOT LIKE \'pg_%\' AND nspname != \'information_schema\'"'
    schemas_output = run_command(schema_cmd, env=admin_env)

    # Process each schema
    if schemas_output:
        for schema in schemas_output.strip().split('\n'):
            schema = schema.strip()
            if not schema:
                continue

            # Reassign ownership of the schema
            schema_owner_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -c "ALTER SCHEMA {schema} OWNER TO {PG_USER}"'
            run_command(schema_owner_cmd, env=admin_env)

            # Get all tables in this schema
            tables_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -t -c "SELECT tablename FROM pg_tables WHERE schemaname = \'{schema}\'"'
            tables_output = run_command(tables_cmd, env=admin_env)

            # Reassign ownership of each table
            if tables_output:
                for table in tables_output.strip().split('\n'):
                    table = table.strip()
                    if not table:
                        continue

                    owner_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -c "ALTER TABLE {schema}.{table} OWNER TO {PG_USER}"'
                    run_command(owner_cmd, env=admin_env)

                    # Also handle sequences for this table (usually for ID columns)
                    seq_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -t -c "SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = \'{schema}\'"'
                    seqs_output = run_command(seq_cmd, env=admin_env)

                    if seqs_output:
                        for seq in seqs_output.strip().split('\n'):
                            seq = seq.strip()
                            if not seq:
                                continue

                            seq_owner_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -c "ALTER SEQUENCE {schema}.{seq} OWNER TO {PG_USER}"'
                            run_command(seq_owner_cmd, env=admin_env)

            # Get all views in this schema
            views_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -t -c "SELECT table_name FROM information_schema.views WHERE table_schema = \'{schema}\'"'
            views_output = run_command(views_cmd, env=admin_env)

            # Reassign ownership of each view
            if views_output:
                for view in views_output.strip().split('\n'):
                    view = view.strip()
                    if not view:
                        continue

                    view_owner_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -c "ALTER VIEW {schema}.{view} OWNER TO {PG_USER}"'
                    run_command(view_owner_cmd, env=admin_env)

            # Get all functions in this schema
            funcs_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -t -c "SELECT routine_name FROM information_schema.routines WHERE routine_schema = \'{schema}\'"'
            funcs_output = run_command(funcs_cmd, env=admin_env)

            # Reassign ownership of each function
            if funcs_output:
                for func in funcs_output.strip().split('\n'):
                    func = func.strip()
                    if not func:
                        continue

                    # For functions, we need to handle overloading, so we use a different approach
                    func_owner_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -c "ALTER FUNCTION {schema}.{func}() OWNER TO {PG_USER}"'
                    run_command(func_owner_cmd, env=admin_env)

    print("Table ownerships fixed")

    # Apply the permissions
    print("Applying permissions...")

    # Modify database permissions to target the restored database
    with open(perm_files['db_perms'], 'r') as f:
        db_perms = f.read().replace(PG_DB, PG_RESTORED_DB)

    modified_db_perms = os.path.join(
        BACKUP_DIR, "permissions", "modified_db_perms.sql")
    with open(modified_db_perms, 'w') as f:
        f.write(db_perms)

    # Apply database permissions - using admin user
    db_perm_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d postgres -f "{modified_db_perms}"'
    run_command(db_perm_cmd, env=admin_env)

    # Apply schema permissions (new)
    schema_perm_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -f "{perm_files["schema_perms"]}"'
    run_command(schema_perm_cmd, env=admin_env)

    # Apply table permissions
    table_perm_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -f "{perm_files["table_perms"]}"'
    run_command(table_perm_cmd, env=admin_env)

    # Apply default privileges
    def_perm_cmd = f'"{psql_path}" -U {PG_ADMIN_USER} -d {PG_RESTORED_DB} -f "{perm_files["def_perms"]}"'
    run_command(def_perm_cmd, env=admin_env)

    # Verify the restored database
    verify_restored_database()

    print(f"Database restore completed successfully to: {PG_RESTORED_DB}")


def verify_restored_database():
    """Verify the restored database has the expected tables and data"""
    print("Verifying restored database...")

    # Set up environment with password
    env = {}
    if PG_USER in passwords:
        env["PGPASSWORD"] = passwords[PG_USER]

    psql_path = os.path.join(PG_BIN_PATH, "psql")

    # Get table count from original database
    original_tables_cmd = f'"{psql_path}" -U {PG_USER} -d {PG_DB} -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema NOT IN (\'pg_catalog\', \'information_schema\')"'
    original_tables = run_command(original_tables_cmd, env=env)
    original_count = int(original_tables.strip()) if original_tables else 0

    # Get table count from restored database
    restored_tables_cmd = f'"{psql_path}" -U {PG_USER} -d {PG_RESTORED_DB} -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema NOT IN (\'pg_catalog\', \'information_schema\')"'
    restored_tables = run_command(restored_tables_cmd, env=env)
    restored_count = int(restored_tables.strip()) if restored_tables else 0

    print(f"Original database has {original_count} tables")
    print(f"Restored database has {restored_count} tables")

    if original_count != restored_count:
        print(
            f"WARNING: Table count mismatch between original ({original_count}) and restored ({restored_count}) databases!")

        # List tables in original database
        list_orig_cmd = f'"{psql_path}" -U {PG_USER} -d {PG_DB} -t -c "SELECT table_schema || \'.\' || table_name FROM information_schema.tables WHERE table_schema NOT IN (\'pg_catalog\', \'information_schema\') ORDER BY table_schema, table_name"'
        orig_tables_list = run_command(list_orig_cmd, env=env)

        # List tables in restored database
        list_rest_cmd = f'"{psql_path}" -U {PG_USER} -d {PG_RESTORED_DB} -t -c "SELECT table_schema || \'.\' || table_name FROM information_schema.tables WHERE table_schema NOT IN (\'pg_catalog\', \'information_schema\') ORDER BY table_schema, table_name"'
        rest_tables_list = run_command(list_rest_cmd, env=env)

        # Write tables to file for comparison
        tables_dir = os.path.join(BACKUP_DIR, "verification")
        os.makedirs(tables_dir, exist_ok=True)

        with open(os.path.join(tables_dir, "original_tables.txt"), 'w') as f:
            f.write(orig_tables_list if orig_tables_list else "No tables found")

        with open(os.path.join(tables_dir, "restored_tables.txt"), 'w') as f:
            f.write(rest_tables_list if rest_tables_list else "No tables found")

        print(f"Table lists written to {tables_dir} for comparison")
    else:
        print("Table count matches between original and restored databases")

    # Check row count in a few tables (sample check)
    print("Checking row counts in sample tables...")
    sample_tables_cmd = f'"{psql_path}" -U {PG_USER} -d {PG_DB} -t -c "SELECT table_schema, table_name FROM information_schema.tables WHERE table_schema NOT IN (\'pg_catalog\', \'information_schema\') ORDER BY RANDOM() LIMIT 3"'
    sample_tables = run_command(sample_tables_cmd, env=env)

    if sample_tables:
        for line in sample_tables.strip().split('\n'):
            if not line.strip():
                continue

            parts = line.strip().split('|')
            if len(parts) == 2:
                schema = parts[0].strip()
                table = parts[1].strip()

                # Get count from original
                orig_count_cmd = f'"{psql_path}" -U {PG_USER} -d {PG_DB} -t -c "SELECT COUNT(*) FROM {schema}.{table}"'
                orig_row_count = run_command(orig_count_cmd, env=env)

                # Get count from restored
                rest_count_cmd = f'"{psql_path}" -U {PG_USER} -d {PG_RESTORED_DB} -t -c "SELECT COUNT(*) FROM {schema}.{table}"'
                rest_row_count = run_command(rest_count_cmd, env=env)

                print(f"Table {schema}.{table}: Original={orig_row_count.strip() if orig_row_count else 'error'}, Restored={rest_row_count.strip() if rest_row_count else 'error'}")


def main():
    global PG_ADMIN_USER

    parser = argparse.ArgumentParser(
        description="PostgreSQL SmartSubs Database Backup and Restore Tool")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--backup", action="store_true",
                       help="Backup the SmartSubs database")
    group.add_argument("--restore", action="store_true",
                       help="Restore the SmartSubs database to a new database")
    group.add_argument("--verify", action="store_true",
                       help="Verify the restored database against the original")
    parser.add_argument("--admin-user", default=PG_ADMIN_USER,
                        help=f"Admin username for operations requiring elevated privileges (default: {PG_ADMIN_USER})")
    parser.add_argument("--use-pgpass", action="store_true",
                        help="Use pgpass file instead of environment variables for passwords")

    args = parser.parse_args()

    # Update admin user if specified
    if args.admin_user:
        PG_ADMIN_USER = args.admin_user

    # Get passwords upfront to avoid multiple prompts
    get_password(PG_USER)
    get_password(PG_ADMIN_USER)

    # Configure pgpass if requested
    if args.use_pgpass:
        pgpass_path = configure_pgpass()
        if pgpass_path:
            print(f"Using pgpass file at: {pgpass_path}")

    if args.backup:
        backup_database()
    elif args.restore:
        restore_database()
    elif args.verify:
        verify_restored_database()


if __name__ == "__main__":
    main()
