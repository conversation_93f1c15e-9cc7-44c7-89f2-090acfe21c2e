#!/bin/bash

echo "Starting servers..."

# Function to cleanup background processes on exit
cleanup() {
    echo "Shutting down services..."
    # Kill all background jobs started by this script
    jobs -p | xargs -r kill
    exit 0
}

# Set up trap to catch Ctrl+C and cleanup
trap cleanup SIGINT SIGTERM

echo "Starting Backend server..."
cd backend
FLASK_ENV=development FLASK_APP=api.py python -m flask run &
BACKEND_PID=$!
cd ..

echo "Starting backend service job..."
cd backend
./run_smartsubs_hourly.sh &
SERVICE_PID=$!
cd ..

echo "Starting React Frontend on port 8080..."
cd frontend-react
npm run start:dev &
FRONTEND_PID=$!
cd ..

echo "Backend server, service job, and React Frontend should now be running."
echo "React Frontend will be available at http://localhost:8080"
echo "Press Ctrl+C to stop all services"

# Wait for all background processes
wait