# tmux configuration for smartersubs project

# Enable mouse support
set -g mouse on

# Enable mouse scrolling in panes
bind -n WheelUpPane if-shell -F -t = "#{mouse_any_flag}" "send-keys -M" "if -Ft= '#{pane_in_mode}' 'send-keys -M' 'select-pane -t=; copy-mode -e; send-keys -M'"
bind -n WheelDownPane select-pane -t= \; send-keys -M

# Allow mouse to select panes and windows
set -g mouse-select-pane on 2>/dev/null || true
set -g mouse-select-window on 2>/dev/null || true
set -g mouse-resize-pane on 2>/dev/null || true

# Improve colors
set -g default-terminal "screen-256color"

# Set status bar colors
set -g status-bg colour235
set -g status-fg colour136

# Highlight active pane
set -g pane-active-border-style fg=colour166

# Set pane border colors
set -g pane-border-style fg=colour235

# Enable activity alerts
setw -g monitor-activity on
set -g visual-activity on

# Start windows and panes at 1, not 0
set -g base-index 1
setw -g pane-base-index 1

# Renumber windows when one is closed
set -g renumber-windows on

# Increase scrollback buffer size
set -g history-limit 10000

# Enable clipboard integration
set -g set-clipboard on

# Allow automatic renaming of windows
set -g allow-rename on

# Set window titles
set -g set-titles on
set -g set-titles-string '#T - #W'

# Display pane titles
set -g pane-border-status top
set -g pane-border-format '#P: #{pane_title}'