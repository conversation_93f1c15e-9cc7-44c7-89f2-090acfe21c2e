# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Architecture

This is a Reddit data aggregation platform called "SmarterSubs" with:

- **Backend**: Python Flask API with PostgreSQL database
  - Reddit API integration using PRAW library
  - Google OAuth2 authentication with JWT tokens
  - Middleware for authentication, CSRF protection, and rate limiting
  - Modular structure with blueprints, models, and middleware
- **Frontend**: React/TypeScript SPA using Vite
  - Context-based state management (Auth, Posts, Theme)
  - Component-based architecture with layout, UI, and page components
  - Google OAuth integration and JWT token handling

## Build & Development Commands

### Full Application
- `./start_servers.sh` - Start both backend and frontend servers (Linux)
- `start_servers.bat` - Start both backend and frontend servers (Windows)

### Frontend (React/TypeScript)
- `cd frontend-react && npm run dev` - Start development server
- `cd frontend-react && npm run build` - Build for production  
- `cd frontend-react && npm run lint` - Lint TypeScript files
- `cd frontend-react && npm run start:dev` - Start dev server on port 8080

### Backend (Python/Flask)
- `cd backend && set FLASK_ENV=development && set FLASK_APP=api.py && python -m flask run` - Run Flask API server
- `cd backend && python smartsubs.py` - Run Reddit data fetching service
- `python dbtest.py` - Test database connection and run diagnostics

## Authentication Flow

The application uses Google OAuth2 with JWT tokens:
1. Frontend initiates Google OAuth via `@react-oauth/google`
2. Backend validates Google token and creates user session
3. JWT access/refresh tokens stored as httpOnly cookies
4. AuthContext manages authentication state and token refresh
5. Protected routes use `ProtectedRoute` component and `require_auth` middleware

## Database Structure

- PostgreSQL database with environment-based configuration
- Main entities: users, user_sessions, posts, target_subreddits
- Database initialization handled in `db.py` with connection pooling
- Environment variables for connection params (PG_HOST, PG_DB, etc.)

## Key Context Providers

- **AuthContext**: User authentication, login/logout, token management
- **PostsContext**: Reddit posts data, search, filtering, expansion state
- **ThemeContext**: Dark/light mode theming

## Code Style Guidelines

### Frontend
- Use functional React components with hooks
- Follow TypeScript strict typing with proper interfaces
- Import order: React, external libraries, internal modules
- Error handling with toast notifications via react-toastify
- Context API for global state, local useState for component state

### Backend
- Follow PEP 8 style guidelines with type hints
- Use Flask blueprints for route organization (`routes/`, `middleware/`)
- Environment-based configuration with `.env.development`/`.env.production`
- Structured logging with dedicated auth logger in `logs/auth.log`
- Database operations use context managers for proper connection handling

### Security Patterns
- JWT tokens in httpOnly cookies only
- CSRF protection for state-changing operations
- Input validation and sanitization
- Rate limiting on API endpoints