# Docker Development Setup

This project uses Docker Compose for managing development dependencies.

## Services

- **PostgreSQL 15**: Database server running on port 5433
- **Redis Alpine**: Cache and session store running on port 6379

## Commands

### Start all services
```bash
docker-compose -f docker-compose.dev.yml up -d
```

### Stop all services
```bash
docker-compose -f docker-compose.dev.yml down
```

### View service status
```bash
docker-compose -f docker-compose.dev.yml ps
```

### View logs
```bash
# All services
docker-compose -f docker-compose.dev.yml logs

# Specific service
docker-compose -f docker-compose.dev.yml logs postgres
docker-compose -f docker-compose.dev.yml logs redis
```

### Reset data volumes (clean slate)
```bash
docker-compose -f docker-compose.dev.yml down -v
docker-compose -f docker-compose.dev.yml up -d
```

## Health Checks

Both services include health checks:
- PostgreSQL: `pg_isready` command
- Redis: `redis-cli ping` command

Services will show as "healthy" once ready to accept connections.