import psycopg2
from psycopg2 import sql
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get credentials from environment variables
USER = os.environ["PG_USER"]
PASSWORD = os.environ["PG_PASSWORD"]
HOST = os.environ["PG_HOST"]
PORT = os.environ["PG_PORT"]
DATABASE = os.environ["PG_DB"]

def test_connection():
    """Test basic connection to the database."""
    try:
        conn = psycopg2.connect(
            dbname=DATABASE,
            user=USER,
            password=PASSWORD,
            host=HOST,
            port=PORT
        )
        print("✓ Successfully connected to database")
        return conn
    except Exception as e:
        print(f"✗ Connection failed: {e}")
        sys.exit(1)

def test_crud_operations(conn):
    """Test Create, Read, Update, Delete operations."""
    try:
        cur = conn.cursor()
        
        # Create a test table
        print("\nTesting table creation...")
        try:
            cur.execute("""
                CREATE TABLE test_table (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL
                )
            """)
            conn.commit()
            print("✓ Successfully created table")
        except Exception as e:
            print(f"✗ Create table failed: {e}")
            conn.rollback()
            return False
        
        # Insert data
        print("\nTesting insert operation...")
        try:
            cur.execute("""
                INSERT INTO test_table (name) VALUES (%s) RETURNING id
            """, ("Test User",))
            row_id = cur.fetchone()[0]
            conn.commit()
            print(f"✓ Successfully inserted row with ID {row_id}")
        except Exception as e:
            print(f"✗ Insert operation failed: {e}")
            conn.rollback()
            return False
            
        # Read data
        print("\nTesting select operation...")
        try:
            cur.execute("SELECT * FROM test_table")
            rows = cur.fetchall()
            print(f"✓ Successfully read {len(rows)} row(s)")
        except Exception as e:
            print(f"✗ Select operation failed: {e}")
            return False
        
        # Update data
        print("\nTesting update operation...")
        try:
            cur.execute("""
                UPDATE test_table SET name = %s WHERE id = %s
            """, ("Updated User", row_id))
            conn.commit()
            print("✓ Successfully updated row")
        except Exception as e:
            print(f"✗ Update operation failed: {e}")
            conn.rollback()
            return False
        
        # Delete data
        print("\nTesting delete operation...")
        try:
            cur.execute("DELETE FROM test_table WHERE id = %s", (row_id,))
            conn.commit()
            print("✓ Successfully deleted row")
        except Exception as e:
            print(f"✗ Delete operation failed: {e}")
            conn.rollback()
            return False
        
        # Drop test table
        print("\nCleaning up by dropping test table...")
        try:
            cur.execute("DROP TABLE test_table")
            conn.commit()
            print("✓ Successfully dropped test table")
        except Exception as e:
            print(f"✗ Drop table failed: {e}")
            conn.rollback()
            return False
            
        return True
        
    except Exception as e:
        print(f"Unexpected error during CRUD tests: {e}")
        return False
    finally:
        if cur:
            cur.close()

def main():
    print("Testing PostgreSQL user permissions for '{}'".format(USER))
    print("-" * 50)
    
    # Test connection
    conn = test_connection()
    
    # Test CRUD operations
    success = test_crud_operations(conn)
    
    # Close connection
    if conn:
        conn.close()
    
    print("\n" + "-" * 50)
    if success:
        print("✅ All tests passed! User setup is correct.")
    else:
        print("❌ Some tests failed. Please check the error messages above.")

if __name__ == "__main__":
    main()