{"$schema": "http://json-schema.org/draft-07/schema#", "title": "High-Value AI Reddit Content", "type": "object", "required": ["mostNewsworthyAIPosts", "mostInsightfulUsefulAIPosts"], "properties": {"mostNewsworthyAIPosts": {"type": "array", "description": "Reddit posts containing breaking news, announcements, or significant AI developments", "items": {"type": "object", "required": ["postId", "title", "relevanceScore", "reason"], "properties": {"postId": {"type": "string", "description": "The unique identifier of the Reddit post"}, "title": {"type": "string", "description": "The title of the Reddit post"}, "relevanceScore": {"type": "number", "minimum": 1, "maximum": 100, "description": "Relevance score on a 1-100 scale indicating importance"}, "reason": {"type": "string", "description": "Explanation for why this post was selected as newsworthy"}}}}, "mostInsightfulUsefulAIPosts": {"type": "array", "description": "Reddit posts providing practical knowledge, tutorials, or thoughtful analysis on AI", "items": {"type": "object", "required": ["postId", "title", "relevanceScore", "reason"], "properties": {"postId": {"type": "string", "description": "The unique identifier of the Reddit post"}, "title": {"type": "string", "description": "The title of the Reddit post"}, "relevanceScore": {"type": "number", "minimum": 1, "maximum": 100, "description": "Relevance score on a 1-100 scale indicating usefulness"}, "reason": {"type": "string", "description": "Explanation for why this post was selected as insightful or useful"}}}}}}