import logging
import yaml
import json
import os
import sys
import datetime
from typing import Dict, List, Optional, Any, Union
from dotenv import load_dotenv

from litellm import completion
from litellm.exceptions import APIError, RateLimitError, ServiceUnavailableError, OpenAIError, AuthenticationError

try:
    import jsonschema
    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False
    logging.warning("jsonschema package not installed. Schema validation will be limited.")
    logging.warning("To install: pip install jsonschema")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("aiutils")

# Load environment variables from .env file
def load_env_vars():
    """
    Load environment variables from .env file.
    First tries to find .env in the aiutils directory, then falls back to the parent directory.
    """
    # Determine the directory of the current script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Try loading from aiutils/.env first
    env_file = os.path.join(script_dir, '.env')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        logger.info(f"Loaded environment variables from {env_file}")
        return True
    
    # If not found, try the parent directory
    parent_env_file = os.path.join(os.path.dirname(script_dir), '.env')
    if os.path.exists(parent_env_file):
        load_dotenv(parent_env_file)
        logger.info(f"Loaded environment variables from {parent_env_file}")
        return True
    
    logger.warning("No .env file found in aiutils directory or parent directory")
    return False

# Load environment variables at module initialization
load_env_vars()

def create_log_directory(base_log_dir: str = "logs") -> str:
    """
    Creates a timestamped directory for logging within the base log directory.
    
    Args:
        base_log_dir (str): Base directory for logs. Defaults to "logs".
        
    Returns:
        str: Path to the created timestamped log directory.
    """
    # Create base log directory if it doesn't exist
    if not os.path.exists(base_log_dir):
        os.makedirs(base_log_dir)
        logger.info(f"Created base log directory: {base_log_dir}")
    
    # Create timestamped subdirectory
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = os.path.join(base_log_dir, timestamp)
    os.makedirs(log_dir)
    logger.info(f"Created log directory: {log_dir}")
    
    return log_dir

def log_api_call(
    log_dir: str,
    prompt: str,
    context: Optional[str],
    schema: Optional[Dict[str, Any]],
    response_text: str,
    parsed_response: Union[Dict[str, Any], List[Any]],
    model_info: Dict[str, Any],
    token_usage: Optional[Dict[str, int]] = None
) -> None:
    """
    Logs API call details to the specified directory.
    
    Args:
        log_dir (str): Directory to save logs in.
        prompt (str): The prompt sent to the model.
        context (Optional[str]): Context provided with the prompt.
        schema (Optional[Dict[str, Any]]): Schema for response validation.
        response_text (str): Raw text response from the model.
        parsed_response (Union[Dict[str, Any], List[Any]]): Parsed JSON response.
        model_info (Dict[str, Any]): Information about the model used.
        token_usage (Optional[Dict[str, int]]): Token usage statistics if available.
    """
    timestamp = datetime.datetime.now().isoformat()
    
    log_data = {
        "timestamp": timestamp,
        "model": model_info,
        "prompt": prompt,
        "context": context,
        "schema": schema,
        "raw_response": response_text,
        "parsed_response": parsed_response,
        "token_usage": token_usage
    }
    
    # Save the log as a JSON file
    log_file = os.path.join(log_dir, f"call_{datetime.datetime.now().strftime('%H%M%S%f')}.json")
    with open(log_file, 'w') as f:
        json.dump(log_data, f, indent=2)
    
    logger.info(f"Logged API call details to {log_file}")

def load_model_configs_from_env() -> List[Dict[str, Any]]:
    """
    Loads model configurations from environment variables.
    Models are loaded in priority order, with MODEL_NAME_1/API_KEY_1 having the highest priority.
    
    Returns:
        List[Dict[str, Any]]: A list of model configurations with necessary parameters,
                             ordered by priority (highest first).
        
    Raises:
        SystemExit: If no valid model configurations are found.
    """
    logger.info("Loading configurations from environment variables")
    loaded_configs = []
    
    # Try to load configurations from environment variables (up to 5 models)
    for i in range(1, 6):
        api_key = os.environ.get(f"API_KEY_{i}")
        model_name = os.environ.get(f"MODEL_NAME_{i}")
        
        if api_key and model_name:
            config_entry = {
                "model_alias": f"model-{i}",
                "litellm_model": model_name,
                "api_key": api_key,
                "priority": i  # Lower number means higher priority
            }
            loaded_configs.append(config_entry)
            logger.info(f"Loaded config {i}: Alias='model-{i}', Model='{model_name}', Priority={i}")
    
    if not loaded_configs:
        logger.error("No valid model configurations found in environment variables.")
        raise ValueError("No valid model configurations found in environment variables.")
    
    logger.info(f"Successfully loaded {len(loaded_configs)} configurations from environment variables.")
    return loaded_configs

def load_model_configs(config_path: str = None) -> List[Dict[str, Any]]:
    """
    DEPRECATED: This function is deprecated. Use load_model_configs_from_env() instead.
    
    Loads a list of LiteLLM configurations from a YAML file or environment variables.
    
    Args:
        config_path (str, optional): Path to the YAML configuration file. If None, will load from environment.
        
    Returns:
        List[Dict[str, Any]]: A list of model configurations with necessary parameters.
        
    Raises:
        FileNotFoundError: If the config file is not found.
        yaml.YAMLError: If there's an error parsing the YAML file.
        SystemExit: If no valid model configurations are found.
    """
    logger.warning("DEPRECATED: load_model_configs() is deprecated. Use load_model_configs_from_env() instead.")
    
    # Return environment configurations
    return load_model_configs_from_env()

def validate_json_response(response_text: str, schema: Optional[Dict[str, Any]] = None) -> Union[Dict[str, Any], List[Any]]:
    """
    Validates that a response text is valid JSON and optionally conforms to a schema.
    
    Args:
        response_text (str): The JSON string to validate.
        schema (Optional[Dict[str, Any]]): JSON schema to validate against.
        
    Returns:
        Union[Dict[str, Any], List[Any]]: The parsed JSON object.
        
    Raises:
        json.JSONDecodeError: If the response is not valid JSON.
        jsonschema.exceptions.ValidationError: If schema validation fails and JSONSCHEMA_AVAILABLE is True.
    """
    try:
        json_response = json.loads(response_text)
        logger.info("Response is valid JSON.")
        
        # Validate against schema if available
        if schema:
            if JSONSCHEMA_AVAILABLE:
                try:
                    jsonschema.validate(instance=json_response, schema=schema)
                    logger.info("Response successfully validated against the schema.")
                except jsonschema.exceptions.ValidationError as e:
                    logger.warning(f"Response does not fully comply with the schema: {e}")
                    # We'll still return the JSON even if it doesn't fully comply with schema
            else:
                # Basic type checking without jsonschema (very limited)
                if isinstance(schema, dict):
                    if schema.get("type") == "array" and isinstance(json_response, list):
                        logger.info("Response appears to match the expected array format (basic check only).")
                    elif schema.get("type") == "object" and isinstance(json_response, dict):
                        logger.info("Response appears to match the expected object format (basic check only).")
                    else:
                        logger.warning("Response may not match the expected schema format. Check the output.")
        
        return json_response
    
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON response: {e}")
        raise

def run_prompt(
    prompt: str, 
    context: str = None, 
    schema: Optional[Dict[str, Any]] = None,
    config_path: Optional[str] = None,  # Kept for backward compatibility but not used
    response_format: Optional[Dict[str, Any]] = None,
    enable_logging: bool = False,
    log_dir: Optional[str] = None
) -> Union[Dict[str, Any], List[Any]]:
    """
    Runs a prompt against available models as defined in environment variables,
    validates the JSON response, and returns it. Models are tried in priority order (1 is highest),
    with the system trying the next model only if the current one fails.
    
    Environment variables should be set in the format:
    - MODEL_NAME_1, API_KEY_1 (highest priority)
    - MODEL_NAME_2, API_KEY_2
    - ...etc (up to MODEL_NAME_5, API_KEY_5)
    
    Args:
        prompt (str): The prompt to send to the model.
        context (str, optional): Additional context to provide to the model.
        schema (Dict[str, Any], optional): JSON schema to validate the response against.
        config_path (Optional[str]): Not used. Kept for backward compatibility.
        response_format (Dict[str, Any], optional): Override response format specification.
        enable_logging (bool): Whether to log API calls. Defaults to False.
        log_dir (Optional[str]): Subdirectory name inside logs/ folder. If None, uses a timestamp.
        
    Returns:
        Union[Dict[str, Any], List[Any]]: The validated JSON response from the model.
        
    Raises:
        ValueError: If prompt is empty or configuration issues occur.
        RuntimeError: If all model attempts fail.
    """
    if not prompt:
        logger.error("Empty prompt provided")
        raise ValueError("Prompt cannot be empty")
    
    # Create log directory if logging is enabled
    current_log_dir = None
    if enable_logging:
        # Base logs directory
        base_logs_dir = "logs"
        if not os.path.exists(base_logs_dir):
            os.makedirs(base_logs_dir)
            logger.info(f"Created base logs directory: {base_logs_dir}")
            
        if log_dir:
            # Create a custom named subdirectory inside logs/
            current_log_dir = os.path.join(base_logs_dir, log_dir)
            if not os.path.exists(current_log_dir):
                os.makedirs(current_log_dir)
                logger.info(f"Created custom log directory: {current_log_dir}")
        else:
            # Create a timestamped subdirectory
            current_log_dir = create_log_directory(base_logs_dir)
    
    # Load model configurations from environment variables only
    try:
        configs = load_model_configs_from_env()
        logger.info("Using model configurations from environment variables")
    except Exception as e:
        logger.error(f"Failed to load model configurations from environment variables: {e}")
        raise ValueError(f"Configuration error: No valid model configurations found in environment variables")
    
    # Prepare system content and response format
    system_content = "Use the provided context to answer the user's prompt.\nYour response MUST be formatted as valid JSON."
    
    # Set default response_format parameter
    if response_format is None:
        response_format_param = {"type": "json_object"}
    else:
        response_format_param = response_format
    
    # Add schema if provided
    if schema:
        system_content += f"\n\nAdhere strictly to this JSON schema:\n{json.dumps(schema, indent=2)}"
        # Add schema to response_format if not already specified
        if "schema" not in response_format_param and response_format_param["type"] == "json_object":
            response_format_param["schema"] = schema
    else:
        system_content += "\n\nFormat your entire response as a single JSON object."
    
    # Add context if provided
    if context:
        system_content += f"\n\nContext:\n{context}"
    
    # Prepare messages
    messages = [
        {"role": "system", "content": system_content},
        {"role": "user", "content": prompt}
    ]
    
    logger.info("Prepared messages with JSON formatting instructions")
    
    # Try each model configuration
    response = None
    last_error = None
    
    for i, config in enumerate(configs):
        litellm_model_name = config["litellm_model"]
        model_alias = config["model_alias"]
        api_key = config["api_key"]  # May be None, litellm will check env vars
        
        logger.info(f"Attempt {i+1}/{len(configs)}: Using model '{model_alias}' ({litellm_model_name})")
        
        try:
            # Call litellm.completion
            current_response = completion(
                model=litellm_model_name,
                messages=messages,
                api_key=api_key,
                response_format=response_format_param
            )
            
            logger.info(f"Successfully received response from model '{model_alias}'")
            
            # Extract response text
            if current_response.choices and current_response.choices[0].message and current_response.choices[0].message.content:
                response_text = current_response.choices[0].message.content
                
                # Token usage data
                token_usage = None
                if current_response.usage:
                    token_usage = {
                        "prompt_tokens": current_response.usage.prompt_tokens,
                        "completion_tokens": current_response.usage.completion_tokens,
                        "total_tokens": current_response.usage.total_tokens
                    }
                    logger.info(f"Token usage: Prompt={token_usage['prompt_tokens']}, "
                               f"Completion={token_usage['completion_tokens']}, "
                               f"Total={token_usage['total_tokens']}")
                
                # Validate JSON
                try:
                    parsed_response = validate_json_response(response_text, schema)
                    
                    # Log API call if enabled
                    if enable_logging and current_log_dir:
                        model_info = {
                            "alias": model_alias,
                            "model": litellm_model_name
                        }
                        log_api_call(
                            current_log_dir,
                            prompt,
                            context,
                            schema,
                            response_text,
                            parsed_response,
                            model_info,
                            token_usage
                        )
                    
                    return parsed_response
                except json.JSONDecodeError:
                    logger.error("Model returned invalid JSON")
                    last_error = ValueError("Model response is not valid JSON")
                    continue  # Try next model
            else:
                logger.error(f"Unexpected response structure from model '{model_alias}'")
                last_error = ValueError(f"Unexpected response structure from model '{model_alias}'")
                continue  # Try next model
            
        except (APIError, RateLimitError, ServiceUnavailableError, OpenAIError, AuthenticationError) as e:
            logger.warning(f"Model '{model_alias}' failed: {type(e).__name__} - {e}")
            last_error = e
            continue  # Try next model
            
        except Exception as e:
            logger.error(f"Unexpected error with model '{model_alias}': {e}", exc_info=True)
            last_error = e
            continue  # Try next model
    
    # If we got here, all models failed
    error_msg = f"All model attempts failed. Last error: {last_error}"
    logger.error(error_msg)
    raise RuntimeError(error_msg)

# Example usage
if __name__ == "__main__":
    # Example 1: Simple prompt with no schema
    try:
        # Load sample context from a file
        with open("input.txt", "r") as f:
            sample_context = f.read()
            
        # Run a simple prompt with context
        result = run_prompt(
            prompt="Summarize the key information in JSON format with name, email, and city fields.",
            context=sample_context,
            enable_logging=True  # Enable logging
        )
        print("Example 1 Result:", json.dumps(result, indent=2))
        
    except Exception as e:
        print(f"Example 1 failed: {e}")
    
    # Example 2: Using a JSON schema
    try:
        # Define a sample schema
        person_schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "email": {"type": "string"},
                "city": {"type": "string"}
            },
            "required": ["name", "email", "city"]
        }
        
        # Run with schema validation
        result = run_prompt(
            prompt="Extract the first person's information.",
            context=sample_context,
            schema=person_schema,
            enable_logging=True  # Enable logging
        )
        print("Example 2 Result:", json.dumps(result, indent=2))
        
    except Exception as e:
        print(f"Example 2 failed: {e}")
        
    # Example 3: Reading schema from file and running prompt
    try:
        # Load schema from file
        with open("response_schema.json", "r") as f:
            file_schema = json.load(f)
            
        # Run with schema from file and custom log directory
        result = run_prompt(
            prompt="List all people in the data.",
            context=sample_context,
            schema=file_schema,
            enable_logging=True,
            log_dir="custom_folder"  # Creates logs/custom_folder/
        )
        print("Example 3 Result:", json.dumps(result, indent=2))
        
    except Exception as e:
        print(f"Example 3 failed: {e}") 