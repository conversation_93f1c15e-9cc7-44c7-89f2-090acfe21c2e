Critically analyse provided Reddit posts to identify high-value AI content. Create JSON object listing up to a maximum of 20% of total posts you have received. 

Type 1: MOST NEWSWORTHY AI POSTS
Criteria:
- Contains breaking news, announcements, or significant developments
- Reports on new AI models, tools, or research papers
- Covers regulatory changes or important industry events
- Has credible sources or references
- Generated substantive discussion (comments)

Type 2: MOST INSIGHTFUL/USEFUL AI POSTS
Criteria:
- Provides practical knowledge, tutorials, or resources
- Offers unique perspectives or thoughtful analysis
- Contains implementable advice or techniques
- Demonstrates real-world applications or case studies
- Has educational value for AI practitioners or enthusiasts

For each post, include:
- Post Id
- Relevance score (1-100 scale)
- Reason

Most Important: 

- Only choose posts that have direct relevance to AI. 

Additional instructions:

- Exclude purely promotional content, memes, and basic questions
- Avoid duplicates or highly similar content

Respond in JSON using provided schema.