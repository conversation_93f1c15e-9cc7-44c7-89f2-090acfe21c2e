#!/usr/bin/env python3
"""
AI Post Enhancement System
Processes Reddit posts to determine newsworthiness and insightfulness using AI.
"""

import os
import sys
import json
import logging
import psycopg2
from psycopg2 import sql
from datetime import datetime
import pathlib
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add parent directory to path to import aiutils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import aiutils

# Create logs directory if it doesn't exist
logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(logs_dir, exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(logs_dir, f"ai_enhance_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"))
    ]
)
logger = logging.getLogger(__name__)

# Database connection parameters
DB_CONFIG = {
    "dbname": os.environ["PG_DB"],
    "user": os.environ["PG_USER"],
    "password": os.environ["PG_PASSWORD"],
    "host": os.environ["PG_HOST"],
    "port": os.environ["PG_PORT"]
}

def ensure_database_fields():
    """Add necessary columns to the posts table if they don't exist."""
    logger.info("Ensuring required database fields exist...")
    
    conn = None
    try:
        # Connect to the database
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        # Add columns if they don't exist
        cur.execute("""
            ALTER TABLE posts 
            ADD COLUMN IF NOT EXISTS AI_PROCESSED BOOLEAN DEFAULT FALSE;
        """)
        
        cur.execute("""
            ALTER TABLE posts 
            ADD COLUMN IF NOT EXISTS NEWSWORTHINESS_SCORE NUMERIC DEFAULT 0;
        """)
        
        cur.execute("""
            ALTER TABLE posts 
            ADD COLUMN IF NOT EXISTS INSIGHTFULNESS_SCORE NUMERIC DEFAULT 0;
        """)
        
        cur.execute("""
            ALTER TABLE posts 
            ADD COLUMN IF NOT EXISTS REASON TEXT;
        """)
        
        conn.commit()
        logger.info("Database fields verified/created successfully")
        
    except Exception as e:
        logger.error(f"Error ensuring database fields: {e}")
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            conn.close()

def get_unprocessed_posts():
    """Retrieve 100 most recent posts that haven't been processed."""
    logger.info("Retrieving unprocessed posts...")
    
    conn = None
    posts = []
    
    try:
        # Connect to the database
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        # Query unprocessed posts
        cur.execute("""
            SELECT ID, Title, Text, Upvotes, Number_of_Comments, Permalink, Subreddit_Name, Flair, URL, Date
            FROM posts
            WHERE AI_PROCESSED IS NOT TRUE
            AND (Upvotes >= 50)
            AND (Flair NOT ILIKE '%%Funny%%' AND Flair NOT ILIKE '%%meme%%' AND Flair NOT ILIKE '%%Humour%%')
            ORDER BY Date DESC
            LIMIT 100;
        """)
        
        # Fetch all posts
        posts = cur.fetchall()
        logger.info(f"Retrieved {len(posts)} unprocessed posts")
        
    except Exception as e:
        logger.error(f"Error retrieving unprocessed posts: {e}")
        raise
    finally:
        if conn:
            conn.close()
    
    return posts

def format_posts_for_ai(posts):
    """Format posts for AI processing."""
    logger.info("Formatting posts for AI processing...")
    
    # Format similar to input.txt from the provided examples
    formatted_posts = []
    for post in posts:
        # Safely unpack post data with default empty values for None
        post_id = post[0] or ""
        title = post[1] or ""
        text = post[2] or ""
        upvotes = post[3] or 0
        comments = post[4] or 0
        permalink = post[5] or ""
        subreddit = post[6] or ""
        flair = post[7] or ""
        url = post[8] or ""
        date = post[9] or ""
        
        # Format the post as a tab-separated line
        formatted_post = f"{post_id}\t\"{title}\"\t\"{text}\"\t{url}\t{date}\t{upvotes}\t{comments}\t{permalink}\t{subreddit}\t{flair}"
        formatted_posts.append(formatted_post)
    
    # Join all posts with newlines
    return "\n".join(formatted_posts)

def process_posts_with_ai(context):
    """Process posts using the AI model."""
    logger.info("Processing posts with AI...")
    
    try:
        # Load prompt from file
        with open(os.path.join(os.path.dirname(__file__), "prompt1.txt"), "r", encoding="utf-8") as f:
            prompt = f.read().strip()
        
        # Load schema from file
        with open(os.path.join(os.path.dirname(__file__), "schema1.json"), "r", encoding="utf-8") as f:
            schema = json.load(f)
        
        # Run the AI model
        result = aiutils.run_prompt(
            prompt=prompt,
            context=context,
            schema=schema,
            config_path="config.yaml",
            enable_logging=True
        )
        
        # Validate result has expected structure
        if not isinstance(result, dict):
            raise ValueError(f"AI result is not a dictionary: {type(result)}")
        
        # Validate required fields exist
        expected_keys = ["mostNewsworthyAIPosts", "mostInsightfulUsefulAIPosts"]
        for key in expected_keys:
            if key not in result:
                logger.warning(f"Expected key '{key}' missing from AI result")
        
        logger.info("AI processing complete")
        return result
        
    except Exception as e:
        logger.error(f"Error processing posts with AI: {e}")
        raise

def update_database_with_results(posts, ai_results):
    """Update the database with AI processing results."""
    logger.info("Updating database with AI results...")
    
    conn = None
    try:
        # Connect to the database
        conn = psycopg2.connect(**DB_CONFIG)
        
        # Use a single transaction for all updates
        conn.autocommit = False
        cur = conn.cursor()
        
        # Create a set of all post IDs
        all_post_ids = {post[0] for post in posts}
        
        # Track which posts have been processed by the AI
        processed_ids = set()
        
        # Process newsworthy posts
        if "mostNewsworthyAIPosts" in ai_results:
            for post in ai_results["mostNewsworthyAIPosts"]:
                post_id = post["postId"]
                processed_ids.add(post_id)
                
                # Update the post
                cur.execute("""
                    UPDATE posts
                    SET 
                        NEWSWORTHINESS_SCORE = %s,
                        REASON = %s
                    WHERE ID = %s;
                """, (
                    post["relevanceScore"],
                    post["reason"],
                    post_id
                ))
        
        # Process insightful posts
        if "mostInsightfulUsefulAIPosts" in ai_results:
            for post in ai_results["mostInsightfulUsefulAIPosts"]:
                post_id = post["postId"]
                
                # Check if this post was already processed as newsworthy
                if post_id in processed_ids:
                    # Just update the insightfulness score
                    cur.execute("""
                        UPDATE posts
                        SET INSIGHTFULNESS_SCORE = %s
                        WHERE ID = %s;
                    """, (
                        post["relevanceScore"],
                        post_id
                    ))
                else:
                    # Update both score and reason
                    cur.execute("""
                        UPDATE posts
                        SET 
                            INSIGHTFULNESS_SCORE = %s,
                            REASON = %s
                        WHERE ID = %s;
                    """, (
                        post["relevanceScore"],
                        post["reason"],
                        post_id
                    ))
                
                processed_ids.add(post_id)
        
        # Set scores to 0 for posts not processed by AI
        unprocessed_ids = all_post_ids - processed_ids
        if unprocessed_ids:
            logger.info(f"Setting zero scores for {len(unprocessed_ids)} posts not categorized by AI")
            for post_id in unprocessed_ids:
                cur.execute("""
                    UPDATE posts
                    SET 
                        NEWSWORTHINESS_SCORE = 0,
                        INSIGHTFULNESS_SCORE = 0
                    WHERE ID = %s;
                """, (post_id,))
        
        # Mark all posts as processed
        # Handle SQL syntax for single items in tuple
        if len(all_post_ids) == 1:
            post_id = list(all_post_ids)[0]
            cur.execute("UPDATE posts SET AI_PROCESSED = TRUE WHERE ID = %s", (post_id,))
        else:
            post_ids = tuple(all_post_ids)
            cur.execute("UPDATE posts SET AI_PROCESSED = TRUE WHERE ID IN %s", (post_ids,))
        
        conn.commit()
        
        logger.info(f"Updated {len(posts)} posts in the database")
        logger.info(f"Posts categorized: {len(processed_ids)} out of {len(posts)}")
        
    except Exception as e:
        logger.error(f"Error updating database: {e}")
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            conn.close()

def main():
    """Main function to run the post enhancement process."""
    try:
        logger.info("Starting AI Post Enhancement process")
        
        # Ensure database fields exist
        ensure_database_fields()
        
        # Get unprocessed posts
        posts = get_unprocessed_posts()
        
        # Check if we have enough posts
        if len(posts) < 100:
            logger.warning(f"Not enough unprocessed posts found. Only {len(posts)} available (need 100)")
            logger.info("Aborting process")
            return
        
        # Format posts for AI processing
        context = format_posts_for_ai(posts)
        
        # Process posts with AI
        ai_results = process_posts_with_ai(context)
        
        # Update database with results
        update_database_with_results(posts, ai_results)
        
        logger.info("AI Post Enhancement process completed successfully")
        
    except Exception as e:
        logger.error(f"AI Post Enhancement process failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 