import aiutils
import json
import os
import logging

# Configure basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    try:
        # Read prompt from prompt1.txt
        with open("prompt1.txt", "r", encoding="utf-8") as f:
            prompt = f.read().strip()
            logger.info(f"Loaded prompt: {prompt[:50]}...")
        
        # Load schema from schema1.json
        with open("schema1.json", "r", encoding="utf-8") as f:
            schema = json.load(f)
            logger.info("Loaded JSON schema")
        
        # Context is optional, check if input.txt exists
        context = None
        if os.path.exists("input.txt"):
            with open("input.txt", "r", encoding="utf-8") as f:
                context = f.read()
                logger.info("Loaded context from input.txt")
        else:
            logger.info("No input.txt found, proceeding without context")
        
        # Run the prompt through aiutils
        logger.info("Running prompt through AI model(s)...")
        result = aiutils.run_prompt(
            prompt=prompt,         
            context=context,
            schema=schema,
            config_path="config.yaml",
            enable_logging=True
        )
        
        # Print and save the result
        print("\nAI Response:")
        print(json.dumps(result, indent=2))
        
        # Save response to file
        with open("result.json", "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2)
            logger.info("Results saved to result.json")
            
    except FileNotFoundError as e:
        logger.error(f"File not found: {e}")
    except json.JSONDecodeError as e:
        logger.error(f"JSON parsing error: {e}")
    except Exception as e:
        logger.error(f"Error: {e}", exc_info=True)

if __name__ == "__main__":
    main()