from flask import Blueprint, request, jsonify, make_response, current_app
import os
import jwt
from datetime import datetime, timedelta, timezone
import logging
import secrets
import json
import time
from middleware.auth import verify_google_token, AuthError, require_auth
from models.user import User
from models.user_session import UserSession
from db import get_dict_db_connection
from utils.cookie_utils import set_auth_cookies, clear_auth_cookies
from middleware.csrf import generate_csrf_token, csrf_protect

# Configure logger
logger = logging.getLogger(__name__)

# Configure auth-specific logger
auth_logger = logging.getLogger('auth')
auth_logger.setLevel(logging.INFO)      # can be tuned via ENV

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Add file handler to auth logger
handler = logging.FileHandler('logs/auth.log')
handler.setFormatter(logging.Formatter(
    '%(asctime)s|%(levelname)s|%(user_id)s|%(event)s|%(status)s|%(message)s'))
auth_logger.addHandler(handler)

def log_auth_event(user_id, event, status='success', **meta):
    """Centralized auth event logging function.
    
    Args:
        user_id: User ID (None for anonymous events)
        event: Event type ('login_success', 'login_failure', 'logout', etc.)
        status: 'success', 'error', 'warning', etc.
        **meta: Additional metadata to include in the log
    """
    extra = {'user_id': user_id, 'event': event, 'status': status}
    auth_logger.info(json.dumps(meta), extra=extra)
    
    # Also write to DB audit table
    with get_dict_db_connection() as (conn, _):
        UserSession.create_audit_log(
            conn, 
            user_id, 
            event, 
            meta.get('ip', request.remote_addr), 
            meta.get('ua', request.user_agent.string),
            details=meta
        )

# Create auth blueprint
auth = Blueprint('auth', __name__)

# JWT Secret key from environment or a default for development
JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY')
if not JWT_SECRET_KEY:
    raise RuntimeError("JWT_SECRET_KEY must be set and non-empty in the environment")

# JWT Expiration time (1 hour)
JWT_EXPIRATION_DELTA = 3600

# Database-backed token blacklist for production security

def cleanup_expired_blacklist_tokens():
    """Cleanup expired tokens from blacklist to prevent database bloat."""
    try:
        with get_dict_db_connection() as (conn, _):
            count = UserSession.cleanup_expired_blacklist_tokens(conn)
            logger.info(f"Cleaned up {count} expired blacklisted tokens")
            return count
    except Exception as e:
        logger.error(f"Failed to cleanup expired blacklist tokens: {e}")
        return 0

def generate_token(user_id, email, google_id):
    """Generate a JWT token for the user."""
    # Periodically cleanup expired blacklist tokens (every ~100 token generations)
    # This prevents database bloat without impacting performance
    if secrets.randbelow(100) == 0:
        cleanup_expired_blacklist_tokens()
    
    # Generate token with a unique ID (jti)
    jti = f"{user_id}-{int(time.time())}-{secrets.token_hex(8)}"
    exp_time = datetime.now(timezone.utc) + timedelta(seconds=JWT_EXPIRATION_DELTA)
    
    payload = {
        'user_id': user_id,
        'email': email,
        'google_id': google_id,
        'exp': exp_time,
        'iat': datetime.now(timezone.utc),
        'jti': jti  # Include the token ID
    }
    token = jwt.encode(payload, JWT_SECRET_KEY, algorithm='HS256')
    return token, jti, exp_time

def is_token_blacklisted(jti):
    """Check if a token is blacklisted."""
    try:
        with get_dict_db_connection() as (conn, _):
            return UserSession.is_token_blacklisted(conn, jti)
    except Exception as e:
        logger.error(f"Error checking token blacklist: {e}")
        # Fail securely - treat as not blacklisted to avoid blocking valid tokens
        return False

def add_to_blacklist(token):
    """Add a token to the blacklist."""
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=['HS256'])
        jti = payload.get('jti')
        exp = payload.get('exp')
        user_id = payload.get('user_id')
        
        if jti and exp and user_id:
            # Convert Unix timestamp to datetime
            exp_datetime = datetime.fromtimestamp(exp)
            
            with get_dict_db_connection() as (conn, _):
                # Add to database blacklist
                blacklist_success = UserSession.add_token_to_blacklist(
                    conn, jti, user_id, exp_datetime, 'logout'
                )
                
                # Also invalidate in user_sessions table for consistency
                session_success = UserSession.invalidate_session(conn, jti)
                
                if not session_success:
                    # If we couldn't find the session in the DB, create a record of the invalidation
                    # This handles tokens issued before we implemented persistent tracking
                    logger.warning(f"Attempted to invalidate session with JTI {jti[:8]}... but no record found")
                    
                    # Create an audit log entry
                    UserSession.create_audit_log(
                        conn, 
                        user_id, 
                        'token_invalidated_missing_session', 
                        request.remote_addr, 
                        request.user_agent.string,
                        details={"jti": jti}
                    )
                
                return blacklist_success
                
    except jwt.PyJWTError as e:
        logger.error(f"Failed to decode token for blacklisting: {str(e)}")
    except Exception as e:
        logger.error(f"Error adding token to blacklist: {str(e)}")
    return False

# Rate limiting is now handled by flask-limiter (configured in api.py)

@auth.route('/google', methods=['POST'])
def google_auth():
    """Authenticate using Google OAuth2 token."""
    try:
        # Apply rate limiting
        client_ip = request.remote_addr
        user_agent = request.user_agent.string
        
        # Get client info if provided
        request_json = request.get_json()
        client_info = request_json.get('client_info', {}) if request_json else {}
        is_chrome_login = client_info.get('isChromeLogin', False)
        
        logger.info(f"Google auth request from: {client_ip}, Chrome login: {is_chrome_login}")
        
        if not request_json:
            logger.error("No request body provided")
            
            # Log error
            log_auth_event(
                None,
                'login_attempt',
                'error',
                reason='no_request_body',
                ip=client_ip,
                ua=user_agent
            )
            
            raise AuthError('No request body', 400)
            
        token = request_json.get('token')
        if not token:
            logger.error("No token provided in request")
            
            # Log error
            log_auth_event(
                None,
                'login_attempt',
                'error',
                reason='no_token',
                ip=client_ip,
                ua=user_agent
            )
            
            raise AuthError('No token provided', 400)
        
        token_length = len(token) if token else 0
        logger.info(f"Attempting to verify Google token of length {token_length}")
        
        # Verify the Google token with special handling for Chrome login
        try:
            google_user_info = verify_google_token(token)
            
            logger.info(f"Successfully verified token for user: {google_user_info.get('email')}")
            
            # Create or update user in database
            with get_dict_db_connection() as (conn, _):
                # Ensure tables exist
                UserSession.create_table_if_not_exists(conn)
                
                # Create/update user
                user = User.create_or_update(conn, google_user_info)
                
                # Generate JWT access token
                access_token, token_jti, exp_time = generate_token(user.id, user.email, user.google_id)
                
                # Generate refresh token
                refresh_token_jti = f"{user.id}-refresh-{int(time.time())}-{secrets.token_hex(8)}"
                refresh_exp_time = datetime.now(timezone.utc) + timedelta(days=30)
                refresh_token_payload = {
                    'user_id': user.id,
                    'email': user.email,
                    'google_id': user.google_id,
                    'exp': refresh_exp_time,
                    'iat': datetime.now(timezone.utc),
                    'jti': refresh_token_jti,
                    'type': 'refresh'
                }
                refresh_token = jwt.encode(refresh_token_payload, JWT_SECRET_KEY, algorithm='HS256')
                
                # Create session record
                UserSession.create_session(
                    conn, 
                    user.id, 
                    token_jti, 
                    exp_time, 
                    client_ip, 
                    user_agent,
                    extra_data={"is_chrome_login": is_chrome_login} if is_chrome_login else None,
                    refresh_jti=refresh_token_jti
                )
                
                # Generate CSRF token
                csrf_token = generate_csrf_token(user.id)
                
                # Create response with user data
                response_data = {
                    'user': {
                        'email': user.email,
                        'name': user.name,
                        'picture': user.picture_url,
                        'google_id': user.google_id,
                        'created_at': user.created_at.isoformat() if user.created_at else None
                    },
                    'csrf_token': csrf_token  # Include CSRF token in response body
                }
                
                # Create response object
                response = make_response(jsonify(response_data))
                
                # Set cookies
                set_auth_cookies(response, access_token, refresh_token)
                
                # Log successful login
                log_auth_event(
                    user.id,
                    'login_success',
                    'success',
                    ip=client_ip,
                    ua=user_agent,
                    email=user.email,
                    google_id=user.google_id
                )
                
                logger.info(f"Login successful for user: {user.email}")
                
                return response
        except Exception as e:
            logger.error(f"Token verification failed: {str(e)}")
            
            # Log token verification failure
            log_auth_event(
                None,
                'login_failure',
                'error',
                reason=str(e),
                ip=client_ip,
                ua=user_agent
            )
            
            raise
            
    except AuthError as e:
        logger.error(f"Auth error during Google authentication: {e.error}")
        
        # Log authentication error
        log_auth_event(
            None,
            'login_failure',
            'error',
            reason=e.error,
            status_code=e.status_code,
            ip=client_ip if 'client_ip' in locals() else None,
            ua=user_agent if 'user_agent' in locals() else None
        )
        
        return jsonify({'error': e.error}), e.status_code
    except Exception as e:
        logger.error(f"Error in Google Auth: {str(e)}")
        
        # Log unexpected error
        log_auth_event(
            None,
            'login_failure',
            'error',
            reason=str(e),
            error_type=type(e).__name__,
            ip=client_ip if 'client_ip' in locals() else None,
            ua=user_agent if 'user_agent' in locals() else None
        )
        
        return jsonify({'error': 'Authentication failed'}), 500

@auth.route('/user', methods=['GET'])
@require_auth
def get_user(user_info):
    """Get current user info from the token."""
    try:
        with get_dict_db_connection() as (conn, _):
            user = User.find_by_google_id(conn, user_info['sub'])
            if not user:
                return jsonify({'error': 'User not found'}), 404
                
            return jsonify({
                'email': user.email,
                'name': user.name,
                'picture': user.picture_url,
                'google_id': user.google_id,
                'created_at': user.created_at.isoformat() if user.created_at else None
            })
    except Exception as e:
        logger.error(f"Error getting user info: {str(e)}")
        return jsonify({'error': 'Failed to retrieve user information'}), 500

@auth.route('/logout', methods=['POST'])
@require_auth             # <-- outermost: first gets user_info 
@csrf_protect()           # <-- innermost: then validates CSRF with user_info
def logout(user_info):
    """Log out the user by invalidating their tokens."""
    try:
        # Get the tokens from cookies or headers
        access_token = request.cookies.get('access_token')
        if not access_token:
            auth_header = request.headers.get('Authorization', '')
            if auth_header.startswith('Bearer '):
                access_token = auth_header.split(' ')[1]
                
        refresh_token = request.cookies.get('refresh_token')
        
        # Revoke the tokens by adding them to the blacklist
        # and invalidating them in the database
        success = False
        
        if access_token:
            success = add_to_blacklist(access_token)
            
        if refresh_token:
            # Also invalidate the refresh token
            add_to_blacklist(refresh_token)
        
        # Log the event
        log_auth_event(
            user_info.get('user_id'),
            'logout',
            'success' if success else 'partial',
            ip=request.remote_addr,
            ua=request.user_agent.string
        )
        
        # Create a response that clears the auth cookies
        response = make_response(jsonify({'success': True}))
        clear_auth_cookies(response)
        
        return response
    except Exception as e:
        # Log the error
        log_auth_event(
            user_info.get('user_id', 'unknown'),
            'logout',
            'error',
            error=str(e),
            ip=request.remote_addr,
            ua=request.user_agent.string
        )
        
        # Return error response
        return jsonify({'error': 'Logout failed'}), 500

@auth.route('/refresh', methods=['POST'])
def refresh_token_endpoint():
    """Refresh an access token using a refresh token."""
    try:
        # Get refresh token from cookie or Authorization header
        refresh_token = request.cookies.get('refresh_token')
        
        # Fall back to authorization header
        if not refresh_token:
            auth_header = request.headers.get('Authorization', '')
            if auth_header.startswith('Bearer '):
                refresh_token = auth_header.split(' ')[1]
        
        if not refresh_token:
            raise AuthError('No refresh token provided', 400)
        
        # Decode and validate token
        try:
            payload = jwt.decode(refresh_token, JWT_SECRET_KEY, algorithms=['HS256'])
            
            # Ensure token is a refresh token
            if payload.get('type') != 'refresh':
                raise AuthError('Invalid token type', 400)
            
            # Check if token has JTI
            jti = payload.get('jti')
            if not jti:
                raise AuthError('Invalid token format', 400)
            
            # Check if token is blacklisted
            if is_token_blacklisted(jti):
                raise AuthError('Token has been revoked', 401)
            
            # Verify token against database
            with get_dict_db_connection() as (conn, _):
                # Check if the refresh token is still valid in the database
                session = UserSession.find_session_by_jti(conn, jti)
                if not session or not session.is_valid:
                    raise AuthError('Token has been revoked', 401)
                
                # Get user information
                user = User.find_by_id(conn, payload['user_id'])
                
                if not user:
                    raise AuthError('User not found', 404)
                
                # Generate new access token
                access_token, access_jti, exp_time = generate_token(
                    user.id, user.email, user.google_id
                )
                
                # Create session record for new token
                UserSession.create_session(
                    conn,
                    user.id,
                    access_jti,
                    exp_time,
                    request.remote_addr,
                    request.user_agent.string,
                    refresh_jti=jti  # Link to the refresh token
                )
                
                # Generate new CSRF token
                csrf_token = generate_csrf_token(user.id)
                
                # Create response
                response_data = {
                    'access_token': access_token,
                    'csrf_token': csrf_token,
                    'user': {
                        'id': user.id,
                        'email': user.email,
                        'name': user.name,
                        'picture_url': user.picture_url
                    }
                }
                
                # Log the refresh event
                log_auth_event(
                    user.id,
                    'token_refresh',
                    'success',
                    ip=request.remote_addr,
                    ua=request.user_agent.string
                )
                
                # Set cookies
                response = make_response(jsonify(response_data))
                set_auth_cookies(response, access_token, refresh_token)
                
                return response
                
        except jwt.ExpiredSignatureError:
            # Log expired token attempt
            try:
                payload = jwt.decode(refresh_token, JWT_SECRET_KEY, algorithms=['HS256'], 
                                    options={"verify_exp": False})
                user_id = payload.get('user_id')
            except:
                user_id = None
                
            log_auth_event(
                user_id,
                'token_refresh',
                'expired',
                ip=request.remote_addr,
                ua=request.user_agent.string
            )
            
            raise AuthError('Refresh token expired', 401)
            
        except jwt.InvalidTokenError as e:
            raise AuthError(f'Invalid refresh token: {str(e)}', 401)
    
    except AuthError as e:
        # Clear cookies on auth error
        response = make_response(jsonify({'error': e.error}))
        clear_auth_cookies(response)
        response.status_code = e.status_code
        return response
        
    except Exception as e:
        logger.error(f"Unexpected error in refresh token endpoint: {str(e)}")
        return jsonify({'error': 'An unexpected error occurred'}), 500

@auth.route('/sessions', methods=['GET'])
@csrf_protect()  # Apply CSRF protection
@require_auth
def get_sessions(user_info):
    """Get active sessions for the current user."""
    try:
        with get_dict_db_connection() as (conn, _):
            # Get user id from google_id
            user = User.find_by_google_id(conn, user_info['sub'])
            if not user:
                return jsonify({'error': 'User not found'}), 404
                
            # Get active sessions
            sessions = UserSession.get_active_sessions(conn, user.id)
            
            # Format sessions for response
            session_list = []
            for session in sessions:
                session_list.append({
                    'id': session.id,
                    'created_at': session.created_at.isoformat() if session.created_at else None,
                    'expires_at': session.expires_at.isoformat() if session.expires_at else None,
                    'ip_address': session.ip_address,
                    'user_agent': session.user_agent,
                    'last_used_at': session.last_used_at.isoformat() if session.last_used_at else None
                })
            
            return jsonify({
                'sessions': session_list,
                'count': len(session_list)
            })
    except Exception as e:
        logger.error(f"Error getting user sessions: {str(e)}")
        return jsonify({'error': 'Failed to retrieve sessions'}), 500 