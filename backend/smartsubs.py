import time
import argparse
from typing import List
from datetime import datetime
from db import get_db_connection, get_dict_db_connection, initialize_database, dict_from_row
from utils import get_reddit_object, get_new_redditposts, write_posts_to_db

def fetch_posts_by_category(reddit, category: str, time_filter=None) -> None:
    """
    Fetch posts from all subreddits in a given category
    
    Args:
        reddit: Reddit instance
        category: The category to fetch posts for
        time_filter: Optional time filter ('hour', 'day', 'week', 'month', 'year', 'all')
    """
    try:
        with get_db_connection() as (conn, cursor):
            # Get all subreddits for this category
            cursor.execute("""
                SELECT DISTINCT Subreddit_Name, Sub_Category 
                FROM target_subreddits 
                WHERE Category = %s
            """, (category,))
            
            subreddits = cursor.fetchall()

        
        if not subreddits:
            print(f"No subreddits found for category: {category}")
            return
            
        for subreddit, sub_category in subreddits:
            try:
                post_start_time = time.time()
                print(f"Fetching posts from {subreddit} (Category: {category}, Sub-category: {sub_category})")
                
                posts_list = get_new_redditposts(subreddit, reddit, time_filter)
                
                if not posts_list:
                    print(f"No posts found for subreddit: {subreddit}")
                    continue
                
                post_end_time = time.time()
                post_exec_time = post_end_time - post_start_time
                print(f"Fetched {len(posts_list)} posts from {subreddit} in {round(post_exec_time, 2)} seconds")
                
                write_posts_to_db(posts_list, subreddit, category)
                
            except Exception as e:
                print(f"Error processing subreddit {subreddit}: {e}")
                continue  # Continue with next subreddit even if one fails
            
    except Exception as e:
        print(f"Error fetching posts for category {category}: {e}")

def fetch_post_comments(reddit, post_id: str) -> None:
    """
    Fetch top-level comments for a specific post and save to database
    
    Args:
        reddit: Reddit instance
        post_id: The Reddit post ID to fetch comments for
    """
    try:
        print(f"Fetching comments for post: {post_id}")
        
        # Get post from database to get the permalink
        with get_db_connection() as (conn, cursor):
            cursor.execute("SELECT Permalink, Subreddit_Name FROM posts WHERE ID = %s", (post_id,))
            result = cursor.fetchone()
            
            if not result:
                print(f"Post {post_id} not found in database")
                return None
                
            permalink, subreddit_name = result
            
            # Fetch the comments from Reddit
            submission = reddit.submission(id=post_id)
            submission.comments.replace_more(limit=0)  # Removes "More Comments" and only gets top-level
            
            # Get current timestamp for last_updated
            current_time = datetime.now()
            
            # Clear existing comments for this post to ensure fresh data
            cursor.execute("DELETE FROM Comments WHERE Post_ID = %s", (post_id,))
            
            # Filter for comments with > 5 votes and store them
            comments_count = 0
            for comment in submission.comments:
                if comment.score > 5:  # Only save comments with more than 5 votes
                    try:
                        cursor.execute('''
                        INSERT INTO Comments (
                            ID, Author, Text, Date, Score, Post_ID, Community, last_updated
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (ID) DO UPDATE SET
                            Author = EXCLUDED.Author,
                            Text = EXCLUDED.Text,
                            Date = EXCLUDED.Date,
                            Score = EXCLUDED.Score,
                            Post_ID = EXCLUDED.Post_ID,
                            Community = EXCLUDED.Community,
                            last_updated = EXCLUDED.last_updated
                        ''', (
                            comment.id,
                            str(comment.author),
                            comment.body,
                            datetime.fromtimestamp(comment.created_utc),
                            comment.score,
                            post_id,
                            subreddit_name,
                            current_time
                        ))
                        comments_count += 1
                    except Exception as e:
                        print(f"Error saving comment {comment.id}: {e}")
                        continue
        
        print(f"Saved {comments_count} comments for post {post_id}")
        return comments_count
        
    except Exception as e:
        print(f"Error fetching comments for post {post_id}: {e}")
        return None

def get_post_comments(post_id: str, reddit=None):
    """
    Get comments for a post from the database, or fetch from Reddit if not available or stale
    
    Args:
        post_id: The Reddit post ID to get comments for
        reddit: Optional Reddit instance to fetch comments if not in DB or stale
        
    Returns:
        List of comment dictionaries or None if failed
    """
    try:
        # First get the last_updated info with regular cursor
        with get_db_connection() as (conn, cursor):
            # Check if we have comments for this post and when they were last updated
            cursor.execute("""
                SELECT last_updated FROM Comments 
                WHERE Post_ID = %s 
                ORDER BY last_updated DESC LIMIT 1
            """, (post_id,))
            
            result = cursor.fetchone()
            comments_are_stale = True
            
            if result and result[0]:
                # No need for manual parsing with psycopg2 for timestamp
                last_updated = result[0]
                current_time = datetime.now()
                
                # Check if comments are less than an hour old
                time_diff_seconds = (current_time - last_updated).total_seconds()
                if time_diff_seconds < 3600:  # 3600 seconds = 1 hour
                    comments_are_stale = False
                    print(f"Using cached comments for post {post_id} (updated {int(time_diff_seconds/60)} minutes ago)")
        
        # If no comments in DB or they're stale, and we have a reddit instance, fetch them
        if (comments_are_stale or not result) and reddit:
            print(f"Comments for post {post_id} are stale or not found, fetching from Reddit")
            fetch_post_comments(reddit, post_id)
        
        # Get comments using the dictionary-enabled connection
        with get_dict_db_connection() as (conn, cursor):
            # Return comments from database
            cursor.execute("SELECT * FROM Comments WHERE Post_ID = %s ORDER BY Score DESC", (post_id,))
            comments = cursor.fetchall()
            
            # Convert to list of dictionaries
            comment_list = []
            for row in comments:
                comment_dict = dict_from_row(row)
                if comment_dict:
                    comment_list.append(comment_dict)
        
        if comment_list:
            return comment_list
        else:
            return []
            
    except Exception as e:
        print(f"Error getting comments for post {post_id}: {e}")
        return None

def get_all_categories() -> List[str]:
    """
    Get all unique categories from the target_subreddits table
    
    Returns:
        List of category names
    """
    try:
        with get_db_connection() as (conn, cursor):
            cursor.execute("SELECT DISTINCT Category FROM target_subreddits")
            categories = [row[0] for row in cursor.fetchall()]
            return categories
    except Exception as e:
        print(f"Error getting categories: {e}")
        return []

# Initialize the database before any operations
initialize_database()

# Main execution
if __name__ == "__main__":
    # Add command line argument parsing
    parser = argparse.ArgumentParser(description="Fetch Reddit posts for analysis")
    parser.add_argument("--time-filter", type=str, choices=["hour", "day", "week", "month", "year", "all"],
                        help="Filter posts by time period (default: no time filter)")
    args = parser.parse_args()
    
    start_time = time.time()
    
    try:
        # Initialize the database before any operations
        if not initialize_database():
            print("Failed to initialize database. Exiting.")
            exit(1)
            
        reddit = get_reddit_object()
        if not reddit:
            print("Failed to initialize Reddit object. Exiting.")
            exit(1)
        
        # Get all categories and process each one
        categories = get_all_categories()
        
        if not categories:
            print("No categories found in database. Exiting.")
            exit(1)
        
        if args.time_filter:
            print(f"Using time filter: {args.time_filter}")
            
        print(f"Found {len(categories)} categories to process")
        
        for category in categories:
            print(f"\nProcessing category: {category}")
            fetch_posts_by_category(reddit, category, args.time_filter)
            
            current_time = time.time()
            execution_time = current_time - start_time
            print(f"Progress update - Total execution time: {round(execution_time, 2)} seconds ({round(execution_time/60, 2)} minutes)")

        end_time = time.time()
        total_execution_time = end_time - start_time
        print(f"\nTotal execution completed in: {round(total_execution_time, 2)} seconds ({round(total_execution_time/60, 2)} minutes)")
        
    except KeyboardInterrupt:
        print("\nProcess interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
