from datetime import datetime
from typing import Optional, Dict
import psycopg2
from psycopg2.extras import RealDictCursor

class User:
    def __init__(self, id: int = None, email: str = None, name: str = None, 
                 google_id: str = None, picture_url: str = None, 
                 created_at: datetime = None, last_login: datetime = None):
        self.id = id
        self.email = email
        self.name = name
        self.google_id = google_id
        self.picture_url = picture_url
        self.created_at = created_at
        self.last_login = last_login
    
    def to_dict(self) -> Dict:
        """Convert User object to a dictionary."""
        return {
            'id': self.id,
            'email': self.email,
            'name': self.name,
            'google_id': self.google_id,
            'picture_url': self.picture_url,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    @classmethod
    def create_or_update(cls, conn, google_user_info: Dict) -> 'User':
        """Create a new user or update an existing one based on Google user info."""
        # Check if user exists
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute(
                "SELECT * FROM users WHERE google_id = %s",
                (google_user_info['sub'],)
            )
            user_record = cursor.fetchone()
            
            now = datetime.utcnow()
            
            if user_record:
                # Update existing user
                cursor.execute(
                    """
                    UPDATE users 
                    SET last_login = %s, name = %s, picture_url = %s
                    WHERE google_id = %s
                    RETURNING id, email, name, google_id, picture_url, created_at, last_login
                    """,
                    (now, google_user_info.get('name'), google_user_info.get('picture'), google_user_info['sub'])
                )
                updated_user = cursor.fetchone()
                conn.commit()
                
                # Convert dict to User object
                user = cls(
                    id=updated_user['id'],
                    email=updated_user['email'],
                    name=updated_user['name'],
                    google_id=updated_user['google_id'],
                    picture_url=updated_user['picture_url'],
                    created_at=updated_user['created_at'],
                    last_login=updated_user['last_login']
                )
            else:
                # Create new user
                cursor.execute(
                    """
                    INSERT INTO users (email, name, google_id, picture_url, created_at, last_login, is_active)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    RETURNING id, email, name, google_id, picture_url, created_at, last_login
                    """,
                    (
                        google_user_info['email'],
                        google_user_info['name'],
                        google_user_info['sub'],
                        google_user_info.get('picture'),
                        now,
                        now,
                        True
                    )
                )
                new_user = cursor.fetchone()
                conn.commit()
                
                # Convert dict to User object
                user = cls(
                    id=new_user['id'],
                    email=new_user['email'],
                    name=new_user['name'],
                    google_id=new_user['google_id'],
                    picture_url=new_user['picture_url'],
                    created_at=new_user['created_at'],
                    last_login=new_user['last_login']
                )
            
            return user
    
    @classmethod
    def find_by_google_id(cls, conn, google_id: str) -> Optional['User']:
        """Find a user by Google ID."""
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute(
                "SELECT * FROM users WHERE google_id = %s",
                (google_id,)
            )
            record = cursor.fetchone()
            
            if not record:
                return None
                
            return cls(
                id=record['id'],
                email=record['email'],
                name=record['name'],
                google_id=record['google_id'],
                picture_url=record['picture_url'],
                created_at=record['created_at'],
                last_login=record['last_login']
            ) 