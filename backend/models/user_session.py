from datetime import datetime, timezone
from typing import Optional, Dict, List
import psycopg2
from psycopg2.extras import RealDictCursor
import logging

logger = logging.getLogger(__name__)

class UserSession:
    """Model for user session tracking and audit."""

    def __init__(self, id: Optional[int] = None, user_id: Optional[int] = None,
                 token_jti: Optional[str] = None,
                 ip_address: Optional[str] = None,
                 user_agent: Optional[str] = None,
                 created_at: Optional[datetime] = None,
                 expires_at: Optional[datetime] = None,
                 is_valid: bool = True,
                 last_used_at: Optional[datetime] = None,
                 refresh_token_jti: Optional[str] = None):
        self.id = id
        self.user_id = user_id
        self.token_jti = token_jti
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.created_at = created_at
        self.expires_at = expires_at
        self.is_valid = is_valid
        self.last_used_at = last_used_at
        self.refresh_token_jti = refresh_token_jti
    
    @classmethod
    def create_table_if_not_exists(cls, conn) -> bool:
        """Creates the user_sessions table if it doesn't exist yet."""
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS user_sessions (
                        id SERIAL PRIMARY KEY,
                        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                        token_jti TEXT UNIQUE NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP NOT NULL,
                        ip_address VARCHAR(45),
                        user_agent TEXT,
                        is_valid BOOLEAN DEFAULT true,
                        last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        refresh_token_jti TEXT REFERENCES user_sessions(token_jti) ON DELETE SET NULL,
                        CONSTRAINT valid_session_period CHECK (expires_at > created_at)
                    );
                    
                    CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id, is_valid);
                    CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token_jti) WHERE is_valid = true;
                    CREATE INDEX IF NOT EXISTS idx_sessions_expiry ON user_sessions(expires_at) WHERE is_valid = true;
                    CREATE INDEX IF NOT EXISTS idx_refresh_token_jti ON user_sessions(refresh_token_jti) WHERE is_valid = true;
                    
                    CREATE TABLE IF NOT EXISTS auth_audit_log (
                        id SERIAL PRIMARY KEY,
                        user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
                        event_type VARCHAR(50) NOT NULL,
                        event_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        ip_address VARCHAR(45),
                        user_agent TEXT,
                        details JSONB
                    );
                    
                    CREATE INDEX IF NOT EXISTS idx_audit_user_time ON auth_audit_log(user_id, event_timestamp);
                    CREATE INDEX IF NOT EXISTS idx_audit_event_time ON auth_audit_log(event_type, event_timestamp);
                    
                    CREATE TABLE IF NOT EXISTS token_blacklist (
                        id SERIAL PRIMARY KEY,
                        token_jti TEXT UNIQUE NOT NULL,
                        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                        blacklisted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP NOT NULL,
                        reason VARCHAR(100) DEFAULT 'logout',
                        CONSTRAINT valid_blacklist_period CHECK (expires_at > blacklisted_at)
                    );
                    
                    CREATE INDEX IF NOT EXISTS idx_blacklist_jti ON token_blacklist(token_jti);
                    CREATE INDEX IF NOT EXISTS idx_blacklist_expiry ON token_blacklist(expires_at);
                    CREATE INDEX IF NOT EXISTS idx_blacklist_user ON token_blacklist(user_id, blacklisted_at);
                """)
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error creating user_sessions table: {e}")
            conn.rollback()
            return False
    
    @classmethod
    def find_session_by_jti(cls, conn, token_jti: str) -> Optional['UserSession']:
        """Find a session by token JTI."""
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute("""
                    SELECT * FROM user_sessions 
                    WHERE token_jti = %s AND is_valid = true
                """, (token_jti,))
                
                row = cursor.fetchone()
                if not row:
                    return None
                    
                return cls(
                    id=row['id'],
                    user_id=row['user_id'],
                    token_jti=row['token_jti'],
                    created_at=row['created_at'],
                    expires_at=row['expires_at'],
                    ip_address=row['ip_address'],
                    user_agent=row['user_agent'],
                    is_valid=row['is_valid'],
                    last_used_at=row['last_used_at'],
                    refresh_token_jti=row.get('refresh_token_jti')
                )
        except Exception as e:
            logger.error(f"Error finding session by JTI: {e}")
            return None

    @classmethod
    def update_session_timestamp(cls, conn, token_jti: str) -> bool:
        """Update the last_used_at timestamp of a session."""
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    UPDATE user_sessions
                    SET last_used_at = CURRENT_TIMESTAMP
                    WHERE token_jti = %s AND is_valid = true
                    RETURNING id
                """, (token_jti,))
                
                result = cursor.fetchone()
                conn.commit()
                return result is not None
        except Exception as e:
            logger.error(f"Error updating session timestamp: {e}")
            conn.rollback()
            return False
    
    @classmethod
    def create_session(cls, conn, user_id: int, token_jti: str,
                      expires_at: datetime,
                      ip_address: Optional[str] = None,
                      user_agent: Optional[str] = None,
                      extra_data: Optional[Dict] = None,
                      refresh_jti: Optional[str] = None) -> Optional['UserSession']:
        """Create a new user session with optional refresh token JTI."""
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Check if columns exist
                cursor.execute("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'user_sessions' AND column_name = 'refresh_token_jti'
                """)
                has_refresh_column = cursor.fetchone() is not None
                
                if has_refresh_column and refresh_jti:
                    cursor.execute("""
                        INSERT INTO user_sessions
                        (user_id, token_jti, expires_at, ip_address, user_agent, refresh_token_jti)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        RETURNING id, user_id, token_jti, created_at, expires_at, 
                                  ip_address, user_agent, is_valid, last_used_at, refresh_token_jti
                    """, (user_id, token_jti, expires_at, ip_address, user_agent, refresh_jti))
                else:
                    cursor.execute("""
                        INSERT INTO user_sessions
                        (user_id, token_jti, expires_at, ip_address, user_agent)
                        VALUES (%s, %s, %s, %s, %s)
                        RETURNING id, user_id, token_jti, created_at, expires_at, 
                                  ip_address, user_agent, is_valid, last_used_at
                    """, (user_id, token_jti, expires_at, ip_address, user_agent))
                    
                session_data = cursor.fetchone()
                conn.commit()
                
                if not session_data:
                    return None
                
                # Create audit log entry with extra data if provided
                cls.create_audit_log(
                    conn, 
                    user_id, 
                    'login_success' if not refresh_jti else 'token_refresh', 
                    ip_address, 
                    user_agent,
                    details=extra_data
                )
                
                # Create UserSession object from row data
                session = cls(
                    id=session_data['id'],
                    user_id=session_data['user_id'],
                    token_jti=session_data['token_jti'],
                    created_at=session_data['created_at'],
                    expires_at=session_data['expires_at'],
                    ip_address=session_data['ip_address'],
                    user_agent=session_data['user_agent'],
                    is_valid=session_data['is_valid'],
                    last_used_at=session_data['last_used_at']
                )
                
                # Add refresh_token_jti if available
                if has_refresh_column and 'refresh_token_jti' in session_data:
                    session.refresh_token_jti = session_data['refresh_token_jti']
                    
                return session
        except Exception as e:
            logger.error(f"Error creating user session: {e}")
            conn.rollback()
            return None
    
    @classmethod
    def invalidate_session(cls, conn, token_jti: str) -> bool:
        """Invalidate a session by token JTI."""
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Get the user_id first for audit logging
                cursor.execute("SELECT user_id FROM user_sessions WHERE token_jti = %s", (token_jti,))
                session_data = cursor.fetchone()
                
                if not session_data:
                    return False
                
                user_id = session_data['user_id']
                
                # Invalidate the session
                cursor.execute("""
                    UPDATE user_sessions
                    SET is_valid = false
                    WHERE token_jti = %s
                    RETURNING id
                """, (token_jti,))
                
                result = cursor.fetchone()
                conn.commit()
                
                # Create audit log entry if successful
                if result:
                    cls.create_audit_log(conn, user_id, 'session_invalidated')
                
                return result is not None
        except Exception as e:
            logger.error(f"Error invalidating session: {e}")
            conn.rollback()
            return False
    
    @classmethod
    def get_active_sessions(cls, conn, user_id: int) -> List['UserSession']:
        """Get all active sessions for a user."""
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute("""
                    SELECT * FROM user_sessions
                    WHERE user_id = %s AND is_valid = true AND expires_at > CURRENT_TIMESTAMP
                    ORDER BY created_at DESC
                """, (user_id,))
                
                sessions = []
                for row in cursor.fetchall():
                    sessions.append(cls(
                        id=row['id'],
                        user_id=row['user_id'],
                        token_jti=row['token_jti'],
                        created_at=row['created_at'],
                        expires_at=row['expires_at'],
                        ip_address=row['ip_address'],
                        user_agent=row['user_agent'],
                        is_valid=row['is_valid'],
                        last_used_at=row['last_used_at'],
                        refresh_token_jti=row.get('refresh_token_jti')
                    ))
                return sessions
        except Exception as e:
            logger.error(f"Error getting active sessions: {e}")
            return []
    
    @classmethod
    def create_audit_log(cls, conn, user_id: int, event_type: str,
                         ip_address: Optional[str] = None,
                         user_agent: Optional[str] = None,
                         details: Optional[Dict] = None) -> bool:
        """Create an audit log entry."""
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO auth_audit_log
                    (user_id, event_type, ip_address, user_agent, details)
                    VALUES (%s, %s, %s, %s, %s)
                """, (user_id, event_type, ip_address, user_agent, 
                      psycopg2.extras.Json(details) if details else None))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error creating audit log: {e}")
            conn.rollback()
            return False
    
    @classmethod
    def add_token_to_blacklist(cls, conn, token_jti: str, user_id: int, 
                               expires_at: datetime, reason: str = 'logout') -> bool:
        """Add a token to the blacklist."""
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO token_blacklist (token_jti, user_id, expires_at, reason)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (token_jti) DO NOTHING
                """, (token_jti, user_id, expires_at, reason))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error adding token to blacklist: {e}")
            conn.rollback()
            return False
    
    @classmethod
    def is_token_blacklisted(cls, conn, token_jti: str) -> bool:
        """Check if a token is blacklisted and still within its expiration period."""
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT 1 FROM token_blacklist 
                    WHERE token_jti = %s AND expires_at > CURRENT_TIMESTAMP
                """, (token_jti,))
                return cursor.fetchone() is not None
        except Exception as e:
            logger.error(f"Error checking token blacklist: {e}")
            return False
    
    @classmethod
    def cleanup_expired_blacklist_tokens(cls, conn) -> int:
        """Remove expired tokens from the blacklist to prevent table bloat."""
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    DELETE FROM token_blacklist 
                    WHERE expires_at <= CURRENT_TIMESTAMP
                """)
                removed_count = cursor.rowcount
                conn.commit()
                logger.info(f"Cleaned up {removed_count} expired blacklisted tokens")
                return removed_count
        except Exception as e:
            logger.error(f"Error cleaning up expired blacklist tokens: {e}")
            conn.rollback()
            return 0