import os
import csv

import psycopg2
from psycopg2.extras import Dict<PERSON>ursor
from contextlib import contextmanager
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Determine which database to use
# DB_TYPE = os.getenv('DB_TYPE', 'postgres')  # Default to postgres

# SQLite configuration
# SQLITE_DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'smartsubs.db')

# PostgreSQL configuration - load from environment variables
PG_HOST = os.environ["PG_HOST"]
PG_PORT = os.environ["PG_PORT"]
PG_DB = os.environ["PG_DB"]
PG_USER = os.environ["PG_USER"]
PG_PASSWORD = os.environ["PG_PASSWORD"]

def get_pg_connection_string():
    """Get PostgreSQL connection string from environment variables"""
    return f"host={PG_HOST} port={PG_PORT} dbname={PG_DB} user={PG_USER} password={PG_PASSWORD}"

def create_postgres_db_if_not_exists():
    """Create the PostgreSQL database if it doesn't exist"""
    try:
        # Connect to the default 'postgres' database to check if our DB exists
        conn = psycopg2.connect(
            host=PG_HOST,
            port=PG_PORT,
            dbname='postgres',
            user=PG_USER,
            password=PG_PASSWORD
        )
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Check if our database exists
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (PG_DB,))
        if cursor.fetchone() is None:
            # Create the database
            cursor.execute(f"CREATE DATABASE {PG_DB}")
            print(f"Database {PG_DB} created successfully")
        
        conn.close()
        return True
    except Exception as e:
        print(f"Error creating database: {e}")
        return False

@contextmanager
def get_db_connection():
    """
    Context manager for PostgreSQL database connections.
    
    Yields:
        tuple: (connection, cursor) for database operations
    """
    # global DB_TYPE - Removed
    conn = None
    try:
        # Removed SQLite block
        conn = psycopg2.connect(get_pg_connection_string())
        cursor = conn.cursor()
        
        # Removed fallback logic
        
        yield conn, cursor
        conn.commit()
    except Exception as e:
        if conn:
            conn.rollback()
        raise e
    finally:
        if conn:
            conn.close()

@contextmanager
def get_dict_db_connection():
    """
    Context manager for PostgreSQL database connections that returns rows as dictionaries.
    
    Yields:
        tuple: (connection, cursor) for database operations with dictionary-style results
    """
    # global DB_TYPE - Removed
    conn = None
    try:
        # Removed SQLite block
        conn = psycopg2.connect(get_pg_connection_string())
        cursor = conn.cursor(cursor_factory=DictCursor)
        
        # Removed fallback logic
        
        yield conn, cursor
        conn.commit()
    except Exception as e:
        if conn:
            conn.rollback()
        raise e
    finally:
        if conn:
            conn.close()

def initialize_database():
    """
    Initialize the PostgreSQL database with the schema.
    
    Returns:
        bool: True if initialization was successful, False otherwise
    """
    try:
        # Ensure the PostgreSQL database exists
        if not create_postgres_db_if_not_exists():
            return False
            
        # Removed SQLite schema logic

        # Apply PostgreSQL schema
        schema_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'schema_postgres.sql')
        with open(schema_file, 'r') as f:
            schema = f.read()
        
        with get_db_connection() as (conn, cursor):
            # In PostgreSQL, we need to execute each statement separately
            for statement in schema.split(';'):
                if statement.strip():
                    cursor.execute(statement)
            
            # Check if target_subreddits is empty
            cursor.execute("SELECT COUNT(*) FROM target_subreddits")
            count = cursor.fetchone()[0]
            
            if count == 0:
                # Import subreddits from CSV
                subreddits_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'subreddits.csv')
                try:
                    with open(subreddits_file, 'r', encoding='utf-8') as file:
                        # Skip header row
                        next(file)
                        for line in csv.reader(file):
                            if len(line) >= 3:  # Ensure we have all required fields
                                cursor.execute('''
                                    INSERT INTO target_subreddits (Subreddit_Name, Category, Sub_Category)
                                    VALUES (%s, %s, %s)
                                    ON CONFLICT (Subreddit_Name) DO NOTHING
                                ''', (line[0], line[1], line[2]))
                except UnicodeDecodeError:
                    # Try with different encoding if UTF-8 fails
                    with open(subreddits_file, 'r', encoding='latin-1') as file:
                        next(file)
                        for line in csv.reader(file):
                            if len(line) >= 3:
                                cursor.execute('''
                                    INSERT INTO target_subreddits (Subreddit_Name, Category, Sub_Category)
                                    VALUES (%s, %s, %s)
                                    ON CONFLICT (Subreddit_Name) DO NOTHING
                                ''', (line[0], line[1], line[2]))
        
        return True
        
    except Exception as e:
        print(f"Error initializing database: {e}")
        return False

# Add helper function to handle dict conversion for PostgreSQL
def dict_from_row(row):
    """Convert a PostgreSQL database row (from DictCursor) to a dictionary"""
    if row is None:
        return None
    
    # Removed SQLite logic
    # For PostgreSQL, row is already a dict-like object
    # from the DictCursor, just convert it to a regular dict
    return dict(row) 