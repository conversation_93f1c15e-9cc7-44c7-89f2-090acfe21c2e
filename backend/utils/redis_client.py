"""Redis client configuration and utilities for SmartSubs."""

import os
import redis
import logging
from typing import Optional, Union

logger = logging.getLogger(__name__)

# Global Redis client instance
_redis_client: Optional[redis.Redis] = None

def get_redis_client() -> redis.Redis:
    """Get or create Redis client instance."""
    global _redis_client
    
    if _redis_client is None:
        redis_url = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
        
        try:
            _redis_client = redis.from_url(
                redis_url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            _redis_client.ping()
            logger.info(f"Connected to Redis at {redis_url}")
            
        except redis.ConnectionError as e:
            logger.error("Failed to connect to Redis at %s: %s", redis_url, e)
            logger.error("Redis connection required for CSRF tokens and rate limiting")
            logger.error("Please ensure Redis is running "
                        "(docker-compose -f docker-compose.dev.yml up -d)")
            raise ConnectionError(f"Redis connection failed: {e}") from e

        except Exception as e:
            logger.error("Unexpected error connecting to Redis: %s", e)
            raise

    return _redis_client


def set_with_ttl(key: str, value: Union[str, int, float], 
                 ttl_seconds: int) -> bool:
    """Set a key-value pair with TTL in Redis."""
    try:
        client = get_redis_client()
        return client.setex(key, ttl_seconds, value)
    except Exception as e:
        logger.error("Error setting Redis key %s: %s", key, e)
        return False


def get_and_delete(key: str) -> Optional[str]:
    """Atomically get a value and delete the key using Lua script."""
    try:
        client = get_redis_client()
        # Use Lua script for atomic get-and-delete
        lua_script = """
        local value = redis.call('GET', KEYS[1])
        if value then
            redis.call('DEL', KEYS[1])
        end
        return value
        """
        get_del_script = client.register_script(lua_script)
        return get_del_script(keys=[key])
    except Exception as e:
        logger.error("Error in atomic get_and_delete for key %s: %s", key, e)
        return None


def get_value(key: str) -> Optional[str]:
    """Get a value from Redis."""
    try:
        client = get_redis_client()
        return client.get(key)
    except Exception as e:
        logger.error("Error getting Redis key %s: %s", key, e)
        return None


def delete_key(key: str) -> bool:
    """Delete a key from Redis."""
    try:
        client = get_redis_client()
        return bool(client.delete(key))
    except Exception as e:
        logger.error("Error deleting Redis key %s: %s", key, e)
        return False


def is_available() -> bool:
    """Check if Redis is available."""
    try:
        client = get_redis_client()
        client.ping()
        return True
    except Exception:
        return False
