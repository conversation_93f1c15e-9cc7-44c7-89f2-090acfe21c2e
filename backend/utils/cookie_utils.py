from flask import Response
import os
import logging

# Determine environment
ENV = os.environ.get('FLASK_ENV', 'development')

# Configuration constants
COOKIE_DOMAIN = os.environ.get('COOKIE_DOMAIN', None)  # None = current domain
COOKIE_PATH = '/'
COOKIE_SECURE = ENV != 'development'  # Only false in development
COOKIE_MAX_AGE = 3600  # 1 hour in seconds
REFRESH_COOKIE_MAX_AGE = 30 * 24 * 60 * 60  # 30 days in seconds

# Log warning if using insecure cookies in development
if not COOKIE_SECURE:
    logging.warning("DEVELOPMENT MODE: Secure cookies disabled! Do not use in production.")

def set_auth_cookies(response: Response, access_token: str, refresh_token: str = None) -> Response:
    """Set authentication cookies on the response object."""
    # Set the access token cookie
    response.set_cookie(
        'access_token',
        value=access_token,
        max_age=COOKIE_MAX_AGE,
        path=COOKIE_PATH,
        domain=COOKIE_DOMAIN,
        secure=COOKIE_SECURE,
        httponly=True,
        samesite='Lax' if ENV == 'development' else 'Strict'  # More permissive in development
    )
    
    # Set the refresh token if provided
    if refresh_token:
        response.set_cookie(
            'refresh_token',
            value=refresh_token,
            max_age=REFRESH_COOKIE_MAX_AGE,
            path=COOKIE_PATH,
            domain=COOKIE_DOMAIN,
            secure=COOKIE_SECURE,
            httponly=True,
            samesite='Lax' if ENV == 'development' else 'Strict'
        )
        
    return response

def clear_auth_cookies(response: Response) -> Response:
    """Clear authentication cookies."""
    response.delete_cookie('access_token', path=COOKIE_PATH, domain=COOKIE_DOMAIN)
    response.delete_cookie('refresh_token', path=COOKIE_PATH, domain=COOKIE_DOMAIN)
    return response 