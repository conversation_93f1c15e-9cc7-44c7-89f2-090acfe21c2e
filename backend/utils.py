import csv
import praw
import os
from datetime import datetime
from db import get_db_connection, get_dict_db_connection, initialize_database
from dotenv import load_dotenv

# Load environment variables - check for .env.development and .env.production
env_file = '.env.development'
if os.path.exists(os.path.join(os.path.dirname(os.path.abspath(__file__)), env_file)):
    load_dotenv(dotenv_path=env_file)
else:
    # Fall back to .env or .env.production if .env.development doesn't exist
    if os.path.exists(os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env.production')):
        load_dotenv(dotenv_path='.env.production')
    else:
        load_dotenv()  # Default to .env

# Use absolute path to the database in the backend directory - Removed unused DB_PATH
# DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'smartsubs.db')

def get_reddit_object():
    client_id = os.environ.get('REDDIT_CLIENT_ID')
    client_secret = os.environ.get('REDDIT_CLIENT_SECRET')
    
    if not client_id or not client_secret:
        print("Warning: Reddit API credentials not found in environment variables")
        print(f"Looking for credentials in {env_file}")
    
    reddit = praw.Reddit(client_id=client_id,
                     client_secret=client_secret,
                     user_agent='smartsubs')
    return reddit

def get_new_redditposts(subreddit_name, reddit, time_filter=None):
    """
    Get posts from a subreddit, optionally filtered by time period.
    
    Args:
        subreddit_name (str): Name of the subreddit
        reddit: Reddit instance
        time_filter (str, optional): Time filter to use ('hour', 'day', 'week', 'month', 'year', 'all')
                                     If None, fetches most recent posts without time filtering
    """
    subreddit = reddit.subreddit(subreddit_name)
    
    if time_filter:
        # For time-based filtering, we need to use a different approach
        # The empty search query with time_filter wasn't working correctly
        if time_filter == 'hour':
            # For hour, we can use the /hot endpoint with the 'hour' parameter
            posts = subreddit.hot(limit=5000, time_filter='hour')
        else:
            # For other time periods, use /top with the appropriate time_filter
            posts = subreddit.top(time_filter=time_filter, limit=5000)
        
        filter_type = f"from the last {time_filter}"
    else:
        # Use the original approach
        posts = subreddit.new(limit=5000)
        filter_type = "most recent"
    
    print(f"Fetching {filter_type} posts from r/{subreddit_name}")
    post_list = []

    try:
        for post in posts:
            post_data = {
                'ID': post.id,
                'Date': datetime.fromtimestamp(post.created_utc),  # Store datetime object directly
                'Title': post.title.encode('utf-8').decode('utf-8'),
                'Flair': 'None' if post.link_flair_text is None else post.link_flair_text.encode('utf-8').decode('utf-8'),
                'URL': post.url,
                'Upvotes': post.score,
                'Number of Comments': post.num_comments,
                'Permalink': post.permalink,
                'Text': 'None' if post.selftext is None else post.selftext.encode('utf-8').decode('utf-8')
            }
            post_list.append(post_data)
    except Exception as e:
        print(f"An error occurred: {e}")
        return []
    
    return post_list

def write_posts_to_db(posts, subreddit_name, setname):
    try:
        with get_db_connection() as (conn, cursor):
            # Insert or update the rows
            for post in posts:
                # For PostgreSQL, using %s instead of ? and ON CONFLICT for upsert
                cursor.execute('''
                INSERT INTO posts (
                    ID, Date, Title, Flair, URL, Upvotes, Number_of_Comments, Permalink, Text, Subreddit_Name, setname
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (ID) DO UPDATE SET
                    Date = EXCLUDED.Date,
                    Title = EXCLUDED.Title,
                    Flair = EXCLUDED.Flair,
                    URL = EXCLUDED.URL,
                    Upvotes = EXCLUDED.Upvotes,
                    Number_of_Comments = EXCLUDED.Number_of_Comments,
                    Permalink = EXCLUDED.Permalink,
                    Text = EXCLUDED.Text,
                    Subreddit_Name = EXCLUDED.Subreddit_Name,
                    setname = EXCLUDED.setname
                    -- AI-related fields preserved by not including them in the update
                ''', (
                    post['ID'],
                    post['Date'],  # Pass datetime object directly to psycopg2
                    post['Title'],
                    post['Flair'],
                    post['URL'],
                    post['Upvotes'],
                    post['Number of Comments'],
                    post['Permalink'],
                    post['Text'],
                    subreddit_name,
                    setname
                ))
            
        return 'Success'

    except Exception as e:
        print(f"An error occurred: {e}")
        return 'Failure'

def get_subreddits():
    with get_db_connection() as (conn, cursor):
        cursor.execute("SELECT * FROM target_subreddits")
        subreddits = cursor.fetchall()
        return subreddits
