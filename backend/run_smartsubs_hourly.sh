#!/bin/bash

# Function to cleanup on exit
cleanup() {
    echo "Stopping hourly service..."
    exit 0
}

# Set up trap to catch signals
trap cleanup SIGINT SIGTERM

while true; do
    echo "Running smartsubs.py..."
    python smartsubs.py --time-filter week
    
    echo "Switching to aiutils folder and running ai_enhance.py..."
    cd ..
    cd aiutils
    python ai_enhance.py
    
    echo "Waiting for 30 minutes before next run..."
    cd ../backend
    
    # Sleep for 1800 seconds (30 minutes) - equivalent to Windows timeout /t 1800
    sleep 1800
done