import secrets
from functools import wraps
from flask import request, jsonify, make_response
import os
import logging
from utils.redis_client import set_with_ttl, get_value, delete_key, is_available, get_and_delete

logger = logging.getLogger(__name__)

# CSRF token TTL in seconds (1 hour)
CSRF_TOKEN_TTL = 3600

def generate_csrf_token(user_id):
    """Generate a new CSRF token for a user and store it in Redis."""
    # Input validation for user_id to prevent Redis key injection
    if not isinstance(user_id, (str, int)) or not str(user_id).replace('-', '').isalnum():
        logger.error(f"Invalid user_id format for CSRF token generation: {user_id}")
        return None
        
    token = secrets.token_hex(32)
    csrf_key = f"csrf_token:{user_id}"
    
    try:
        success = set_with_ttl(csrf_key, token, CSRF_TOKEN_TTL)
        if not success:
            logger.error(f"Failed to store CSRF token for user {user_id} in Redis")
            return None
    except Exception as e:
        logger.error(f"Redis error generating CSRF token for user {user_id}: {e}")
        return None
        
    logger.debug(f"Generated CSRF token for user {user_id} (stored in Redis)")
    return token

def validate_csrf_token(user_id, token):
    """Validate a CSRF token for a user using Redis storage."""
    # Input validation for user_id to prevent Redis key injection
    if not isinstance(user_id, (str, int)) or not str(user_id).replace('-', '').isalnum():
        logger.error(f"Invalid user_id format for CSRF token validation: {user_id}")
        return False
        
    csrf_key = f"csrf_token:{user_id}"
    
    try:
        # Atomically get and delete the token to prevent race conditions
        stored_token = get_and_delete(csrf_key)
        
        if not stored_token:
            logger.warning(f"No CSRF token found for user {user_id}")
            return False
        
        # Use constant-time comparison to prevent timing attacks
        if not secrets.compare_digest(stored_token, token):
            logger.warning(f"Invalid CSRF token for user {user_id}")
            # Token was already deleted by get_and_delete, invalid attempt consumed it
            return False
        
        # Token is valid and was already consumed atomically
        logger.debug(f"CSRF token validated and consumed for user {user_id}")
        return True
        
    except Exception as e:
        logger.error(f"Redis error validating CSRF token for user {user_id}: {e}")
        return False

def _extract_user_id(args, kwargs):
    """Extract user_id from args or kwargs, handling both positional and keyword arguments.
    
    Args can have user_info as first argument (from require_auth) or in kwargs.
    """
    # 1) via kwargs
    user = kwargs.get('user_info')
    if user and isinstance(user, dict):
        return user.get('user_id')
    # 2) first positional (require_auth passes user_info as first arg)
    if args and isinstance(args[0], dict):
        return args[0].get('user_id')
    return None

def csrf_protect(exempt_methods=None):
    """Decorator to enforce CSRF protection."""
    if exempt_methods is None:
        exempt_methods = ['GET', 'HEAD', 'OPTIONS']
        
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Skip CSRF validation for exempt methods
            if request.method in exempt_methods:
                return f(*args, **kwargs)
                
            # Get the CSRF token from the request header
            csrf_token = request.headers.get('X-CSRF-Token')
            if not csrf_token:
                logger.warning("CSRF token missing in request")
                return jsonify({'error': 'CSRF token missing'}), 403
                
            # Get user_id from the access token (assuming middleware.auth already verified it)
            user_id = _extract_user_id(args, kwargs)
            if not user_id:
                logger.warning("User not authenticated for CSRF protected endpoint")
                return jsonify({'error': 'Authentication required'}), 401
                
            # Validate the token
            if not validate_csrf_token(user_id, csrf_token):
                # Check if Redis is available to determine appropriate error response
                if not is_available():
                    logger.error("CSRF validation failed due to Redis unavailability")
                    return jsonify({'error': 'Service temporarily unavailable'}), 503
                return jsonify({'error': 'Invalid CSRF token'}), 403
                
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def create_csrf_token_response(user_id):
    """Create a CSRF token and return appropriate response."""
    token = generate_csrf_token(user_id)
    if token is None:
        logger.error(f"Failed to generate CSRF token for user {user_id}")
        return jsonify({'error': 'Service temporarily unavailable'}), 503
    
    return jsonify({'csrf_token': token}), 200