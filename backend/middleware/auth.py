from typing import Optional, Dict, Any
from google.oauth2 import id_token
from google.auth.transport import requests
from functools import wraps
from flask import request, jsonify, current_app
import os
import jwt
from datetime import datetime, timedelta
import logging

# Get logger
logger = logging.getLogger(__name__)

class AuthError(Exception):
    def __init__(self, error: str, status_code: int):
        self.error = error
        self.status_code = status_code

def get_google_client_id():
    return os.environ.get('GOOGLE_CLIENT_ID')

def verify_google_token(token: str) -> Optional[Dict[str, Any]]:
    try:
        GOOGLE_CLIENT_ID = get_google_client_id()
        if not GOOGLE_CLIENT_ID:
            raise AuthError('Google client ID not configured', 500)
            
        # Add logging to help debug token verification issues
        logger.info(f"Verifying Google token, first 10 chars: {token[:10]}..., length: {len(token)}")
        
        try:
            # More detailed verification with extended debugging for Chrome tokens
            idinfo = id_token.verify_oauth2_token(
                token, 
                requests.Request(), 
                GOOGLE_CLIENT_ID,
                clock_skew_in_seconds=300  # Very generous clock skew tolerance for Chrome
            )
            
            # Log success but don't include sensitive info
            issuer = idinfo.get('iss', 'unknown')
            audience = idinfo.get('aud', 'unknown')
            email = idinfo.get('email', 'unknown')
            
            logger.info(f"Token verified: email={email}, issuer={issuer}, audience_matches={audience == GOOGLE_CLIENT_ID}")
            
            if idinfo['aud'] != GOOGLE_CLIENT_ID:
                logger.error(f"Invalid audience: expected {GOOGLE_CLIENT_ID}, got {idinfo['aud']}")
                raise ValueError('Invalid audience')
                
            if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                logger.error(f"Invalid issuer: {idinfo['iss']}")
                raise ValueError('Invalid issuer')
                
            return idinfo
            
        except ValueError as e:
            logger.error(f"Google token validation error: {str(e)}")
            raise
            
    except ValueError as e:
        logger.error(f"Google token verification error: {str(e)}")
        raise AuthError(str(e), 401)
    except Exception as e:
        logger.error(f"Unexpected error during Google token verification: {type(e).__name__}: {str(e)}")
        raise AuthError("Authentication failed", 401)

def verify_jwt_token(token: str) -> Dict[str, Any]:
    """Verify the JWT token."""
    try:
        JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY')
        if not JWT_SECRET_KEY:
            raise RuntimeError("JWT_SECRET_KEY must be set and non-empty in the environment")
            
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=['HS256'])
        
        # Check if token is a refresh token
        if payload.get('type') == 'refresh':
            raise jwt.InvalidTokenError('Cannot use refresh token for authentication')
        
        # Import here to avoid circular imports
        from routes.auth import is_token_blacklisted
        
        # Check if token is blacklisted
        if 'jti' in payload and is_token_blacklisted(payload['jti']):
            raise AuthError('Token has been revoked', 401)

        # Only check the database if we have a JTI
        if 'jti' in payload:
            # Import here to prevent circular imports
            from models.user_session import UserSession
            from db import get_dict_db_connection
            
            # Check if the session exists and is valid in the database
            with get_dict_db_connection() as (conn, _):
                session = UserSession.find_session_by_jti(conn, payload['jti'])
                
                # Only invalidate if the session exists but is explicitly marked as invalid
                if session and not session.is_valid:
                    raise AuthError("Token has been revoked", 401)
                
                # If no session found, the token might be from before we started tracking
                # or there could be a database synchronization issue - let it pass
                # but log a warning
                if not session:
                    logger.warning(f"JWT token with JTI {payload['jti'][:8]}... not found in database")
            
        return payload
    except jwt.ExpiredSignatureError:
        raise AuthError('Token expired', 401)
    except jwt.InvalidTokenError as e:
        raise AuthError(f'Invalid token: {str(e)}', 401)

def require_auth(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        # Try to get the token from cookie first, then Authorization header
        token = request.cookies.get('access_token')
        
        # Fall back to Authorization header if no cookie
        if not token:
            auth_header = request.headers.get('Authorization', None)
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
        
        if not token:
            raise AuthError('No authentication credentials', 401)
            
        try:
            # Validate the token
            payload = verify_jwt_token(token)
            
            # For JWT tokens, get the user info from the database based on google_id
            from models.user import User
            from db import get_dict_db_connection
            
            with get_dict_db_connection() as (conn, _):
                user = User.find_by_google_id(conn, payload['google_id'])
                if not user:
                    raise AuthError('User not found', 404)
                
                # Convert user object to dict for consistency
                user_info = {
                    'sub': user.google_id,
                    'email': user.email,
                    'name': user.name,
                    'picture': user.picture_url,
                    'user_id': user.id
                }
                
                # Update last_used timestamp for the session
                from models.user_session import UserSession
                if 'jti' in payload:
                    UserSession.update_session_timestamp(conn, payload['jti'])
                
                return f(user_info, *args, **kwargs)
                
        except ImportError:
            raise AuthError('Internal server error', 500)
        except AuthError as e:
            raise e
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            raise AuthError('Authentication failed', 401)
            
    return decorated

def handle_auth_error(e):
    response = jsonify({"error": e.error})
    response.status_code = e.status_code
    
    # Add security headers
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    
    return response 