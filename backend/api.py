import logging
import math
import os
import sys
from datetime import datetime, timedelta

# Load environment variables from .env file
from dotenv import load_dotenv
from flask import Flask, jsonify, render_template, request, send_from_directory

# Determine which env file to load based on FLASK_ENV
flask_env = os.environ.get('FLASK_ENV', 'development')
env_file = f".env.{flask_env}"

# Load the appropriate .env file
if os.path.exists(env_file):
    load_dotenv(env_file)
    print(f"Loaded environment from {env_file}")
else:
    print(f"Warning: {env_file} not found, using default environment variables")

# Configure logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add current directory to path to ensure imports work properly
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Import local modules
import smartsubs
import utils
from db import get_db_connection, get_dict_db_connection
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from middleware.auth import AuthError, handle_auth_error
from utils.redis_client import get_redis_client, is_available

app = Flask(__name__)

# Initialize rate limiter with Redis backend
def get_limiter_storage_uri():
    """Get storage URI for flask-limiter. Fails hard if Redis unavailable."""
    redis_url = os.environ.get('REDIS_URL')
    if not redis_url:
        logger.error("REDIS_URL environment variable not set. Rate limiting requires Redis.")
        raise ValueError("REDIS_URL environment variable is required for rate limiting")
    
    # Test Redis connectivity at startup rather than falling back to memory
    try:
        from utils.redis_client import get_redis_client
        get_redis_client().ping()
        logger.info(f"Using Redis for rate limiting: {redis_url}")
        return redis_url
    except Exception as e:
        logger.error(f"Redis connection failed for rate limiting: {e}")
        logger.error("Rate limiting requires Redis. Please ensure Redis is running.")
        raise ConnectionError(f"Redis connection failed for rate limiting: {e}")

def get_rate_limit_key():
    """Get rate limiting key based on user authentication status."""
    from flask import g

    # Check if user is authenticated (set by auth middleware)
    if hasattr(g, 'user_id') and g.user_id:
        return f"user:{g.user_id}"
    
    # Fallback to IP address for anonymous users
    return get_remote_address()

limiter = Limiter(
    key_func=get_rate_limit_key,
    storage_uri=get_limiter_storage_uri(),
    default_limits=["1000 per hour", "100 per minute"]  # Global default limits
)
limiter.init_app(app)

# Rate limiting error handler
@app.errorhandler(429)
def ratelimit_handler(e):
    """Handle rate limiting errors."""
    logger.warning(f"Rate limit exceeded for IP: {request.remote_addr}")
    return jsonify({
        'error': 'Rate limit exceeded',
        'message': 'Too many requests, please try again later'
    }), 429

# Get the allowed origin from environment variable
ALLOWED_ORIGINS = [os.environ.get('CORS_ALLOW_ORIGIN', 'http://localhost:8080')]

# Production domains
ALLOWED_ORIGINS.extend([
    "https://app.smartersubs.io", 
    "https://www.smartersubs.io",
    # Add any additional production domains as needed
])

logger.debug(f"CORS Allowed Origins: {ALLOWED_ORIGINS}")

# Debug middleware to log CORS requests
@app.before_request
def log_request_info():
    origin = request.headers.get('Origin')
    logger.debug(f"Request Origin: {origin}")
    logger.debug(f"Is in allowed origins: {origin in ALLOWED_ORIGINS}")
    logger.debug(f"Request Path: {request.path}")
    logger.debug(f"Request Method: {request.method}")
    logger.debug(f"Request Headers: {dict(request.headers)}")

CORS(app,
     resources={r"/*": {"origins": ALLOWED_ORIGINS}},
     supports_credentials=True,  # Required for cookies
     allow_headers=["Content-Type", "Authorization", "X-CSRF-Token"],
     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
     expose_headers=["Content-Disposition"]
)

# Register auth routes
from routes.auth import auth

app.register_blueprint(auth, url_prefix='/api/v1/auth')


# Register auth error handler
app.errorhandler(AuthError)(handle_auth_error)

# Rate limiting for auth routes is configured via global defaults
# Specific routes can override with @limiter.limit() decorators if needed

# Add security headers to all responses
@app.after_request
def add_security_headers(response):
    # Basic security headers
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    
    # Enhanced Content Security Policy
    # Restrict image sources to HTTPS and data URIs
    csp = "default-src 'self'; " \
          "script-src 'self' https://accounts.google.com; " \
          "frame-src https://accounts.google.com; " \
          "img-src 'self' https: data:; " \
          "style-src 'self' 'unsafe-inline'; " \
          "connect-src 'self'"
    response.headers['Content-Security-Policy'] = csp
    
    # HSTS - only in production
    if os.environ.get('FLASK_ENV') == 'production':
        response.headers['Strict-Transport-Security'] = 'max-age=********; includeSubDomains'
    
    # Add Vary header to prevent cached CORS responses
    response.headers['Vary'] = 'Origin'
    
    # Only add CORS credentials header for allowed origins
    origin = request.headers.get('Origin')
    if origin in ALLOWED_ORIGINS and response.headers.get('Access-Control-Allow-Origin'):
        response.headers['Access-Control-Allow-Credentials'] = 'true'
    
    # Log response headers for debugging
    logger.debug(f"Response Headers: {dict(response.headers)}")
    logger.debug(f"CORS Headers: Access-Control-Allow-Origin={response.headers.get('Access-Control-Allow-Origin')}, Access-Control-Allow-Credentials={response.headers.get('Access-Control-Allow-Credentials')}")
    
    return response

@app.route('/api')
def api_index():
    return jsonify({
        'status': 'online',
        'message': 'SmartSubs API is running',
        'available_endpoints': [
            '/api/v1/posts/technology',
            '/api/v1/comments/:post_id',
            '/api/v1/posts/:post_id/summary',
            '/api/v1/auth/google',
            '/api/v1/auth/user',
            '/api/v1/auth/logout',
            '/api/v1/auth/refresh'
        ]
    })

@app.route('/api/v1/posts/technology', methods=['GET'])
def get_filtered_technology_posts():
    # Get query parameters
    limit = request.args.get('limit', 1000, type=int)
    page = request.args.get('page', 1, type=int)
    date_param = request.args.get('date', None)
    all_dates = request.args.get('all_dates', 'false').lower() == 'true'
    min_upvotes = request.args.get('min_upvotes', 50, type=int)
    min_comments = request.args.get('min_comments', 0, type=int)
    insightful = request.args.get('insightful', 'false').lower() == 'true'
    newsworthy = request.args.get('newsworthy', 'false').lower() == 'true'
    
    # Validate parameters
    if limit < 1 or limit > 1000:
        return jsonify({'code': 400, 'message': 'Limit must be between 1 and 1000'}), 400
    if page < 1:
        return jsonify({'code': 400, 'message': 'Page must be at least 1'}), 400
    
    # If all_dates is true, we don't filter by date
    if all_dates:
        date_condition = "1=1"  # SQL that's always true
        date_params = ()
    else:
        # If no date is provided, default to current date
        if date_param is None:
            target_date = datetime.now().date()
        else:
            try:
                # Parse date from YYYY-MM-DD format
                target_date = datetime.strptime(date_param, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'code': 400, 'message': 'Invalid date format. Use YYYY-MM-DD'}), 400
        
        date_condition = "DATE(Date) = %s"
        date_params = (target_date,)
    
    # Calculate offset for pagination
    offset = (page - 1) * limit
    
    try:
        with get_dict_db_connection() as (conn, cursor):
            # Build parameterized query conditions and parameters
            base_conditions = [
                "(Upvotes >= %s)",
                "(" + date_condition + ")",
                "(number_of_comments >= %s)",
                "(Flair NOT ILIKE %s AND Flair NOT ILIKE %s AND Flair NOT ILIKE %s)",
                "setname = %s"
            ]
            
            # Start with base parameters
            base_params = [min_upvotes] + list(date_params) + [min_comments, '%Funny%', '%meme%', '%Humour%', 'Technology']
            
            # Add conditions for insightful/newsworthy filtering if selected
            if insightful and newsworthy:
                base_conditions.append("(INSIGHTFULNESS_SCORE > %s OR NEWSWORTHINESS_SCORE > %s)")
                base_params.extend([0, 0])
            elif insightful:
                base_conditions.append("INSIGHTFULNESS_SCORE > %s")
                base_params.append(0)
            elif newsworthy:
                base_conditions.append("NEWSWORTHINESS_SCORE > %s")
                base_params.append(0)
                
            # Count total matching records
            count_query = "SELECT COUNT(*) FROM posts WHERE " + " AND ".join(base_conditions)
            cursor.execute(count_query, base_params)
            total_count = cursor.fetchone()[0]
            
            # Get paginated results
            data_query = """
            SELECT *, NEWSWORTHINESS_SCORE, INSIGHTFULNESS_SCORE, REASON 
            FROM posts WHERE """ + " AND ".join(base_conditions) + """
            ORDER BY Date DESC
            LIMIT %s OFFSET %s
            """
            
            # Add pagination parameters
            data_params = base_params + [limit, offset]
            cursor.execute(data_query, data_params)
            posts = cursor.fetchall()
            
            # Convert to list of dictionaries
            post_list = []
            for post in posts:
                post_dict = {
                    'id': post['id'],
                    'date': post['date'].isoformat() if isinstance(post['date'], datetime) else post['date'],
                    'title': post['title'],
                    'flair': post['flair'],
                    'url': post['url'],
                    'upvotes': post['upvotes'],
                    'number_of_comments': post['number_of_comments'],
                    'permalink': post['permalink'],
                    'text': post['text'],
                    'subreddit_name': post['subreddit_name'],
                    'setname': post['setname']
                }
                
                # Add AI scores if they exist
                if 'newsworthiness_score' in post and post['newsworthiness_score'] is not None:
                    post_dict['newsworthiness_score'] = float(post['newsworthiness_score'])
                
                if 'insightfulness_score' in post and post['insightfulness_score'] is not None:
                    post_dict['insightfulness_score'] = float(post['insightfulness_score'])
                
                if 'reason' in post and post['reason'] is not None:
                    post_dict['reason'] = post['reason']
                
                post_list.append(post_dict)
            
            # Calculate pagination info
            total_pages = math.ceil(total_count / limit)
            
            # Return response
            response = {
                'data': post_list,
                'pagination': {
                    'total': total_count,
                    'page': page,
                    'limit': limit,
                    'pages': total_pages
                }
            }
            
            return jsonify(response), 200
            
    except Exception as e:
        app.logger.error(f"Error in /api/v1/posts/technology: {e}", exc_info=True)
        return jsonify({'code': 500, 'message': 'Internal Server Error - Check logs for details'}), 500

@app.route('/api/v1/comments/<post_id>', methods=['GET'])
def get_post_comments(post_id):
    """
    Get comments for a specific post
    """
    try:
        # Check if post exists
        with get_db_connection() as (conn, cursor):
            cursor.execute("SELECT COUNT(*) FROM posts WHERE id = %s", (post_id,))
            post_exists = cursor.fetchone()[0] > 0

            if not post_exists:
                return jsonify({'code': 404, 'message': 'Post not found'}), 404

        # Get comments from DB or fetch from Reddit if needed
        reddit = utils.get_reddit_object()
        comments = smartsubs.get_post_comments(post_id, reddit)

        if comments is None:  # Error occurred
            return jsonify({'code': 500, 'message': 'Error retrieving comments'}), 500

        # Format comments for response
        comment_list = []
        for comment in comments:
            comment_list.append({
                'id': comment['id'],
                'author': comment['author'],
                'text': comment['text'],
                'date': comment['date'].isoformat() if isinstance(comment['date'], datetime) else comment['date'],
                'score': comment['score'],
                'post_id': comment['post_id'],
                'community': comment['community']
            })

        return jsonify({
            'data': comment_list,
            'post_id': post_id,
            'count': len(comment_list)
        }), 200

    except Exception as e:
        app.logger.error(f"Error in /api/v1/comments/{post_id}: {e}", exc_info=True)
        return jsonify({'code': 500, 'message': 'Internal Server Error - Check logs for details'}), 500

@app.route('/api/v1/posts/<post_id>/summary', methods=['GET'])
@limiter.limit("10 per minute")  # Rate limit AI summary requests
def get_post_summary(post_id):
    """
    Generate an AI summary of a post and its comments using Gemini
    """
    try:
        # Check if post exists and get post data
        with get_db_connection() as (conn, cursor):
            cursor.execute("""
                SELECT id, title, text, url, subreddit_name, upvotes, number_of_comments
                FROM posts WHERE id = %s
            """, (post_id,))
            post_result = cursor.fetchone()

            if not post_result:
                return jsonify({'code': 404, 'message': 'Post not found'}), 404

            post_data = {
                'id': post_result[0],
                'title': post_result[1],
                'text': post_result[2] or '',
                'url': post_result[3] or '',
                'subreddit': post_result[4],
                'upvotes': post_result[5],
                'comment_count': post_result[6]
            }

        # Get comments from DB or fetch from Reddit if needed
        reddit = utils.get_reddit_object()
        comments = smartsubs.get_post_comments(post_id, reddit)

        if comments is None:  # Error occurred
            return jsonify({'code': 500, 'message': 'Error retrieving comments'}), 500

        # Import aiutils for AI processing
        sys.path.append(os.path.join(current_dir, '..', 'aiutils'))
        import aiutils

        # Prepare context for AI summary
        context = f"""
POST DETAILS:
Title: {post_data['title']}
Subreddit: r/{post_data['subreddit']}
Upvotes: {post_data['upvotes']}
Comments: {post_data['comment_count']}
URL: {post_data['url']}

POST CONTENT:
{post_data['text']}

COMMENTS ({len(comments)} total):
"""

        # Add top comments to context (limit to top 20 to avoid token limits)
        for i, comment in enumerate(comments[:20]):
            context += f"\n{i+1}. Author: {comment['author']} (Score: {comment['score']})\n{comment['text']}\n"

        # Define the prompt for summarization
        prompt = """
Please provide a comprehensive summary of this Reddit post and its comments. Your summary should include:

1. **Main Topic**: What is the post about?
2. **Key Points**: What are the main points discussed in the post content?
3. **Community Response**: What are the main themes, opinions, or insights from the comments?
4. **Notable Discussions**: Any particularly interesting or highly-voted comment threads?
5. **Overall Sentiment**: What's the general sentiment of the discussion?

Keep the summary concise but informative, focusing on the most important and interesting aspects of the discussion.
"""

        # Define schema for the response
        schema = {
            "type": "object",
            "properties": {
                "summary": {
                    "type": "object",
                    "properties": {
                        "main_topic": {"type": "string"},
                        "key_points": {"type": "array", "items": {"type": "string"}},
                        "community_response": {"type": "string"},
                        "notable_discussions": {"type": "array", "items": {"type": "string"}},
                        "overall_sentiment": {"type": "string"}
                    },
                    "required": ["main_topic", "key_points", "community_response", "overall_sentiment"]
                }
            },
            "required": ["summary"]
        }

        # Generate AI summary
        ai_result = aiutils.run_prompt(
            prompt=prompt,
            context=context,
            schema=schema,
            enable_logging=True
        )

        # Validate result
        if not isinstance(ai_result, dict) or 'summary' not in ai_result:
            raise ValueError("Invalid AI response format")

        return jsonify({
            'post_id': post_id,
            'summary': ai_result['summary'],
            'post_title': post_data['title'],
            'comment_count': len(comments)
        }), 200

    except Exception as e:
        app.logger.error(f"Error generating summary for post {post_id}: {e}", exc_info=True)
        return jsonify({'code': 500, 'message': 'Error generating summary'}), 500

@app.errorhandler(404)
def not_found(e):
    return jsonify({'code': 404, 'message': 'Resource not found'}), 404

@app.errorhandler(500)
def server_error(e):
    return jsonify({'code': 500, 'message': 'Internal server error'}), 500

if __name__ == '__main__':
    app.run(debug=True) 