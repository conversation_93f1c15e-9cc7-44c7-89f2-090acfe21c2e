-- Drop existing tables for clean setup
DROP TABLE IF EXISTS auth_audit_log CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Create users table with Google auth fields
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    google_id TEXT UNIQUE NOT NULL,
    picture_url TEXT,
    created_at TIMESTAMP NOT NULL,
    last_login TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    account_status VARCHAR(50) DEFAULT 'active' CHECK (account_status IN ('active', 'suspended', 'deleted')),
    CONSTRAINT email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- User sessions table with better security
CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    token_jti TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45),  -- Support for IPv6
    user_agent TEXT,
    is_valid BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    refresh_token_jti TEXT REFERENCES user_sessions(token_jti) ON DELETE SET NULL,
    CONSTRAINT valid_session_period CHECK (expires_at > created_at)
);

-- Audit log for security events
CREATE TABLE IF NOT EXISTS auth_audit_log (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    event_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    details JSONB
);

CREATE TABLE IF NOT EXISTS posts (
    ID TEXT PRIMARY KEY,
    Date TIMESTAMP,
    Title TEXT,
    Flair TEXT,
    URL TEXT,
    Upvotes INTEGER,
    Number_of_Comments INTEGER,
    Permalink TEXT,
    Text TEXT,
    Subreddit_Name TEXT,
    setname TEXT,
    AI_PROCESSED BOOLEAN DEFAULT FALSE,
    NEWSWORTHINESS_SCORE NUMERIC DEFAULT 0,
    INSIGHTFULNESS_SCORE NUMERIC DEFAULT 0,
    REASON TEXT
);

CREATE TABLE IF NOT EXISTS Comments (
    ID TEXT PRIMARY KEY,
    Author TEXT,
    Text TEXT,
    Date TIMESTAMP,
    Score INTEGER,
    Post_ID TEXT REFERENCES posts(ID) ON DELETE CASCADE,
    Community TEXT,
    last_updated TIMESTAMP
);

CREATE TABLE IF NOT EXISTS subreddit_searches (
    ID SERIAL PRIMARY KEY,
    Query TEXT,
    Subreddit_Name TEXT,
    Member_Count INTEGER,
    Description TEXT,
    URL TEXT
);

CREATE TABLE IF NOT EXISTS target_subreddits (
    Subreddit_Name TEXT PRIMARY KEY,
    Category TEXT,
    Sub_Category TEXT,
    Created_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_posts_date ON posts(Date);
CREATE INDEX IF NOT EXISTS idx_posts_setname ON posts(setname);
CREATE INDEX IF NOT EXISTS idx_comments_post_id ON Comments(Post_ID);
CREATE INDEX IF NOT EXISTS idx_target_subreddits_category ON target_subreddits(Category);
CREATE INDEX IF NOT EXISTS idx_posts_ai_processed ON posts(AI_PROCESSED);
CREATE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(account_status, is_active);
CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id, is_valid);
CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token_jti) WHERE is_valid = true;
CREATE INDEX IF NOT EXISTS idx_sessions_expiry ON user_sessions(expires_at) WHERE is_valid = true;
CREATE INDEX IF NOT EXISTS idx_audit_user_time ON auth_audit_log(user_id, event_timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_event_time ON auth_audit_log(event_type, event_timestamp);
CREATE INDEX IF NOT EXISTS idx_refresh_token_jti ON user_sessions(refresh_token_jti) WHERE is_valid = true;

-- Add migration support
-- These statements will only execute when upgrading an existing deployment
ALTER TABLE IF EXISTS user_sessions 
ADD COLUMN IF NOT EXISTS refresh_token_jti TEXT REFERENCES user_sessions(token_jti) ON DELETE SET NULL; 