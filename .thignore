venv/
__pycache__/
*.pyc
*.pyo
*.pyd
*.pyw
*.pyz
result.json
logs/
.env
tokens.txt
*.db
.vite/


#th-start

input.txt
tokens_old.txt
.env.development
.env.production
tech-spec-maker/
.env
*.html
*.svg
*.mpd
*.bat
*.woff2
package-lock.json
output.txt
.cursorignore
node_modules\
backend\.env
.gitignore
tokens.txt
token_maker.bat
.githooks
.r2tignore.txt
start_servers_tmux.sh

!security_scanning.md
!security_rport_claude_codev1.md
!security_rport_claude*.md

*.md