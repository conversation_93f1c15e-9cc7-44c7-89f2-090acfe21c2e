#!/bin/bash

echo "Starting servers in separate tmux panes with mouse support..."

# Start PostgreSQL database container if not running
echo "Checking PostgreSQL database container..."
if ! docker ps --format "table {{.Names}}" | grep -q "^smartsubs-postgres$"; then
    echo "Starting PostgreSQL database container..."
    docker run -d --name smartsubs-postgres \
        -e POSTGRES_DB=smartsubs \
        -e POSTGRES_USER=smartsubsuser \
        -e POSTGRES_PASSWORD='Yeahyeah22!' \
        -p 5433:5432 \
        postgres:15
    echo "Waiting for database to be ready..."
    sleep 5
else
    echo "PostgreSQL container already running"
fi

# Function to gracefully stop services
cleanup_services() {
    if tmux has-session -t smartsubs 2>/dev/null; then
        echo "Existing tmux session found. Stopping services gracefully..."
        
        # Stop each service with Ctrl+C in each pane
        echo "Stopping Backend API..."
        tmux send-keys -t smartsubs:servers.0 C-c 2>/dev/null
        
        echo "Stopping Frontend..."
        tmux send-keys -t smartsubs:servers.1 C-c 2>/dev/null
        
        echo "Stopping Background Services..."
        tmux send-keys -t smartsubs:servers.2 C-c 2>/dev/null
        
        # Give services time to shut down gracefully
        echo "Waiting for services to shut down..."
        sleep 3
        
        # Kill the session
        echo "Closing tmux session..."
        tmux kill-session -t smartsubs 2>/dev/null
        
        echo "Cleanup complete."
        echo ""
    fi
}

# Clean up any existing session
cleanup_services

# Load tmux configuration
tmux source-file .tmux.conf 2>/dev/null || echo "Using default tmux config"

# Create new tmux session with first pane
tmux new-session -d -s smartsubs -n servers

# Enable mouse support for this session
tmux set-option -t smartsubs mouse on

# Split into 3 panes
tmux split-window -h -t smartsubs:servers
tmux split-window -v -t smartsubs:servers.1

# Set pane titles first
tmux select-pane -t smartsubs:servers.0 -T "Backend API (Flask :5000)"
tmux select-pane -t smartsubs:servers.1 -T "Frontend (React :8080)"
tmux select-pane -t smartsubs:servers.2 -T "Background Services"

# Start services in each pane
echo "Starting Backend API server..."
tmux send-keys -t smartsubs:servers.0 'cd backend && FLASK_ENV=development FLASK_APP=api.py python -m flask run' Enter

echo "Starting React Frontend..."
tmux send-keys -t smartsubs:servers.1 'cd frontend-react && npm run start:dev' Enter

echo "Starting Background Services..."
tmux send-keys -t smartsubs:servers.2 'cd backend && ./run_smartsubs_hourly.sh' Enter

echo ""
echo "✅ All services started in tmux session 'smartsubs' with mouse support"
echo ""
echo "To view:"
echo "  tmux attach -t smartsubs"
echo ""
echo "Mouse controls (enabled):"
echo "  🖱️  Click panes to switch between them"
echo "  🖱️  Drag pane borders to resize"
echo "  🖱️  Scroll wheel to scroll through output"
echo "  🖱️  Right-click for context menu"
echo ""
echo "Keyboard controls:"
echo "  Ctrl+B then Arrow keys - Switch between panes"
echo "  Ctrl+B then C         - Create new window"
echo "  Ctrl+B then D         - Detach (keeps running)"
echo "  Ctrl+C in each pane   - Stop individual services"
echo ""
echo "To kill all services:"
echo "  tmux kill-session -t smartsubs"
echo ""

# Attach to the session
tmux attach -t smartsubs